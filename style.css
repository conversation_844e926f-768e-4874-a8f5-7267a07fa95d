/* cairo-regular - latin */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Cairo';
  font-style: normal;
  font-weight: 400;
  src: url('cairo-v28-latin-regular.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* cairo-600 - latin */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Cairo';
  font-style: normal;
  font-weight: 600;
  src: url('cairo-v28-latin-600.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* cairo-700 - latin */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Cairo';
  font-style: normal;
  font-weight: 700;
  src: url('cairo-v28-latin-700.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
:root {
    --primary-color: #1976D2; /* Blue */
    --secondary-color: #42A5F5; /* Lighter Blue */
    --background-color: #f4f4f4;
    --text-color: #333;
    --card-background: #ffffff;
    --border-color: #e0e0e0;
    --sidebar-width: 250px;
    --header-height: 60px;
    --transition-speed: 0.3s;
    --success-color: #4CAF50;
    --delete-color: #f44336;
}

[data-theme="dark"] {
    --primary-color: #2196F3;
    --secondary-color: #64B5F6;
    --background-color: #121212;
    --text-color: #e0e0e0;
    --card-background: #1e1e1e;
    --border-color: #444;
    --success-color: #81C784;
    --delete-color: #E57373;
}

* { margin: 0; padding: 0; box-sizing: border-box; }
body { font-family: 'Cairo', sans-serif; background-color: var(--background-color); color: var(--text-color); transition: background-color var(--transition-speed), color var(--transition-speed); overflow-x: hidden; }
.icon-button { background: none; border: none; color: var(--text-color); font-size: 1.5rem; cursor: pointer; padding: 10px; border-radius: 50%; transition: background-color var(--transition-speed); }
.icon-button:hover { background-color: rgba(0, 0, 0, 0.1); }
[data-theme="dark"] .icon-button { color: #e0e0e0; }
[data-theme="dark"] .icon-button:hover { background-color: rgba(255, 255, 255, 0.1); }

/* Header */
.header { position: fixed; top: 0; left: 0; right: 0; height: var(--header-height); background-color: var(--card-background); display: flex; justify-content: space-between; align-items: center; padding: 0 15px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); z-index: 1000; border-bottom: 1px solid var(--border-color); transition: background-color var(--transition-speed), border-color var(--transition-speed); }
.header .title { font-size: 1.5rem; color: var(--primary-color); font-weight: 700; }

/* Sidebar */
.sidebar { position: fixed; top: 0; right: -100%; width: var(--sidebar-width); height: 100%; background-color: var(--card-background); box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1); z-index: 1100; transition: right var(--transition-speed) ease-in-out; display: flex; flex-direction: column; border-left: 1px solid var(--border-color); }
.sidebar.show { right: 0; }
.sidebar-header { padding: 20px; text-align: center; border-bottom: 1px solid var(--border-color); color: var(--primary-color); font-weight: 600; }
.nav-links { list-style: none; flex-grow: 1; padding-top: 15px; }
.nav-link { display: flex; align-items: center; padding: 15px 25px; color: var(--text-color); text-decoration: none; font-size: 1.1rem; transition: background-color var(--transition-speed), color var(--transition-speed); }
.nav-link i { margin-left: 15px; width: 20px; text-align: center; color: var(--primary-color); transition: color var(--transition-speed); }
.nav-link:hover, .nav-link.active { background-color: var(--primary-color); color: #fff; }
.nav-link:hover i, .nav-link.active i { color: #fff; }
.sidebar-footer { padding: 15px; border-top: 1px solid var(--border-color); text-align: center; }
.sidebar-footer .icon-button { width: 100%; text-align: right; border-radius: 5px; font-size: 1.1rem; display: flex; align-items: center; }
.sidebar-footer .icon-button i { margin-left: 10px; color: var(--primary-color); }

/* Main Content */
.main-content { margin-top: var(--header-height); padding: 20px; transition: margin-right var(--transition-speed); margin-right: 0; min-height: calc(100vh - var(--header-height)); }
body.sidebar-open .main-content { filter: blur(2px) brightness(0.7); pointer-events: none; }

/* Page Content Base (Loaded content will be inside this) */
.page-content {
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 25px;
    animation: fadeIn 0.5s ease-in-out;
}
@keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }

/* Home Page Specific (Example) */
.home-container { text-align: center; }

/* Loading Spinner */
.loading-spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 50px auto;
}
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

/* Settings Card & Overlay */
.settings-card { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%) scale(0.8); background-color: var(--card-background); padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); z-index: 1200; width: 90%; max-width: 400px; opacity: 0; visibility: hidden; transition: transform var(--transition-speed), opacity var(--transition-speed), visibility var(--transition-speed); }
.settings-card.show { transform: translate(-50%, -50%) scale(1); opacity: 1; visibility: visible; }
.settings-content { text-align: center; }
.settings-content h4 { margin-bottom: 15px; color: var(--primary-color); font-size: 1.3rem; }
.settings-content p { margin-bottom: 25px; font-size: 0.95rem; line-height: 1.6; }
.delete-button { background-color: var(--delete-color); color: white; padding: 12px 25px; border: none; border-radius: 5px; cursor: pointer; font-size: 1rem; transition: background-color var(--transition-speed); }
.delete-button:hover { background-color: #d32f2f; }
.close-button { position: absolute; top: 10px; left: 10px; background: none; border: none; font-size: 1.8rem; cursor: pointer; color: var(--text-color); }
.overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 999; opacity: 0; visibility: hidden; transition: opacity var(--transition-speed), visibility var(--transition-speed); }
.overlay.show { opacity: 1; visibility: visible; }

/* Toast Message */
.toast { visibility: hidden; min-width: 280px; background-color: var(--success-color); color: #fff; text-align: center; border-radius: 8px; padding: 16px; position: fixed; z-index: 1500; left: 50%; transform: translateX(-50%); bottom: 30px; font-size: 1.1rem; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); opacity: 0; transition: visibility 0.5s, opacity 0.5s, bottom 0.5s; display: flex; align-items: center; justify-content: center; }
.toast i { margin-left: 10px; font-size: 1.3rem; }
.toast.show { visibility: visible; opacity: 1; bottom: 50px; }
.toast.hide { opacity: 0; bottom: 30px; }

/* ... (جميع التنسيقات السابقة كما هي) ... */

/* Responsive adjustments for global elements */
@media (max-width: 768px) {
    .sidebar {
        width: 240px; /* يمكن تعديل عرض الشريط الجانبي إذا لزم الأمر */
    }

    .main-content {
        padding: 15px; /* تقليل الحشو الداخلي للمحتوى الرئيسي */
    }

    .header .title {
        font-size: 1.3rem; /* تصغير عنوان الرأس قليلاً */
    }

    .icon-button {
        font-size: 1.3rem; /* تصغير حجم الأيقونات قليلاً */
        padding: 8px;
    }

    /* تحسين مظهر بطاقة الإعدادات على الشاشات الصغيرة */
    .settings-card {
        padding: 20px;
        width: 95%;
    }
    .settings-content h4 {
        font-size: 1.2rem;
    }
    .settings-content p {
        font-size: 0.9rem;
    }

    /* تحسين التوست على الشاشات الصغيرة */
    .toast {
        min-width: auto;
        width: 90%;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .header .title {
        font-size: 1.1rem;
    }
    .sidebar-header h3 {
        font-size: 1.1rem;
    }
    .nav-link {
        font-size: 1rem;
        padding: 12px 20px;
    }
    .nav-link i {
        margin-left: 12px;
    }

    /* تصغير حجم الخطوط في المحتوى بشكل عام */
    .page-content h2 {
        font-size: 1.5rem; /* مثال: h2 داخل الصفحات المحملة */
        margin-bottom: 20px;
    }
    .page-content p, .page-content li {
        font-size: 0.95rem;
    }
}