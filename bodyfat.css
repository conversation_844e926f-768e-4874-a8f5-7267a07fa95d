/* bodyfat.css - Styles for Enhanced Body Fat Percentage Calculator */

.bodyfat-calculator-container { max-width: 800px; margin: 0 auto; }
.bodyfat-calculator-container h2 { text-align: center; color: var(--primary-color); margin-bottom: 15px; font-size: 1.8rem; }
.bodyfat-calculator-container h2 i { margin-left: 10px; }
.bodyfat-calculator-container .description { text-align: center; color: #888; margin-bottom: 25px; font-size: 1.1rem; }

.calculator-card { background-color: var(--card-background); padding: 25px 30px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.05); border: 1px solid var(--border-color); margin-bottom: 25px; }
.calculator-card h3 { text-align: center; color: var(--primary-color); margin-top: 0; margin-bottom: 25px; font-size: 1.5rem; }
.calculator-card h3 i { margin-left: 8px; }
.calculator-card h4 { font-size: 1.2rem; color: var(--secondary-color); margin-bottom: 15px; text-align: right; border-bottom: 1px solid var(--border-color); padding-bottom: 10px;}


.units-options { display: flex; border: 1px solid var(--border-color); border-radius: 8px; overflow: hidden; margin-bottom: 10px; }
.units-options input[type="radio"] { display: none; }
.units-options label { flex: 1; padding: 12px; text-align: center; cursor: pointer; background-color: var(--background-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; font-size: 1rem; font-weight: 600; margin-bottom: 0; display: flex; align-items: center; justify-content: center; }
.units-options label i { margin-left: 8px; }
.units-options input[type="radio"]:checked + label { background-color: var(--primary-color); color: #fff; }
.units-options label:first-of-type { border-left: 1px solid var(--border-color); }


.calculator-method-tabs { display: flex; margin-bottom: 20px; border-radius: 8px; overflow: hidden; border: 1px solid var(--primary-color); }
.tab-button { flex: 1; padding: 12px 15px; background-color: var(--card-background); color: var(--primary-color); border: none; cursor: pointer; font-size: 1.05rem; font-weight: 600; transition: background-color 0.3s, color 0.3s; }
.tab-button:not(:last-child) { border-left: 1px solid var(--primary-color); }
.tab-button.active, .tab-button:hover { background-color: var(--primary-color); color: #fff; }
[data-theme="dark"] .tab-button { background-color: var(--background-color); color: var(--secondary-color); }
[data-theme="dark"] .tab-button.active, [data-theme="dark"] .tab-button:hover { background-color: var(--secondary-color); color: var(--background-color); }

.shared-inputs-card .input-row { display: flex; gap: 20px; flex-wrap: wrap; }
.shared-inputs-card .input-row .input-group { flex: 1; min-width: 200px; margin-bottom: 10px;}


.method-section.hidden { display: none !important; }
.input-group { margin-bottom: 18px; }
.input-group label { display: block; margin-bottom: 8px; font-weight: 600; color: var(--text-color); font-size: 0.95rem;}
.input-group input[type="number"] { width: 100%; padding: 10px 12px; border: 1px solid var(--border-color); border-radius: 6px; font-size: 1rem; background-color: var(--background-color); color: var(--text-color); font-family: 'Cairo', sans-serif; }
.input-group input:focus { outline: none; border-color: var(--secondary-color); box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.15); }

.gender-options { display: flex; border: 1px solid var(--border-color); border-radius: 8px; overflow: hidden; }
.gender-options input[type="radio"] { display: none; }
.gender-options label { flex: 1; padding: 10px; text-align: center; cursor: pointer; background-color: var(--background-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; font-size: 0.95rem; font-weight: 600; margin-bottom: 0; display: flex; align-items: center; justify-content: center; }
.gender-options label i { margin-left: 8px; }
.gender-options input[type="radio"]:checked + label { background-color: var(--primary-color); color: #fff; }
.gender-options label:first-of-type { border-left: 1px solid var(--border-color); }

#calculate-bf-navy-btn, #calculate-bf-bmi-btn { width: 100%; padding: 14px; border: none; border-radius: 8px; font-size: 1.1rem; cursor: pointer; transition: background-color 0.3s; font-weight: 600; display: flex; justify-content: center; align-items: center; background-color: var(--primary-color); color: #fff; margin-top: 15px; }
#calculate-bf-navy-btn i, #calculate-bf-bmi-btn i { margin-left: 8px; }
#calculate-bf-navy-btn:hover, #calculate-bf-bmi-btn:hover { background-color: var(--secondary-color); }
.error-message { color: var(--delete-color); text-align: center; margin-top: 10px; font-weight: 600; min-height: 1em; }

/* Results Display */
#bodyfat-result-display { border-top: 5px solid var(--success-color); }
#bodyfat-result-display .result-item { display: flex; justify-content: space-between; align-items: center; padding: 12px 5px; border-bottom: 1px solid var(--border-color); font-size: 1.05rem; }
#bodyfat-result-display .result-item:last-of-type { border-bottom: none; }
#bodyfat-result-display .result-item span { color: var(--text-color); }
#bodyfat-result-display .result-item strong { color: var(--primary-color); font-weight: 700; }
.category-badge { padding: 5px 12px; border-radius: 15px; font-size: 0.95rem; color: #fff !important; font-weight: 600 !important; }
.category-badge.athlete { background-color: #1976D2; } .category-badge.fitness { background-color: #4CAF50; }
.category-badge.average { background-color: #FFC107; color: #333 !important; } .category-badge.obese { background-color: #f44336; }
[data-theme="dark"] .category-badge.average { color: #000 !important; }

/* Info & Tips Section */
#bodyfat-info-tips { border-top: 5px solid var(--secondary-color); }
#bodyfat-info-tips p, #bodyfat-info-tips ul li { margin-bottom: 12px; line-height: 1.7; font-size: 0.95rem; color: var(--text-color); }
#bodyfat-info-tips h4 { font-size: 1.15rem; color: var(--primary-color); margin-top: 18px; margin-bottom: 10px; }
#bodyfat-info-tips ul { padding-right: 20px; list-style: disc; }
#bodyfat-info-tips ul ul { margin-top: 5px; padding-right: 25px; list-style: circle; }
#category-specific-tips ul li::marker { color: var(--primary-color); }
.disclaimer { text-align: center; font-size: 0.85rem; color: #888; margin-top: 20px; font-style: italic; }

/* History Section */
.bodyfat-history-section { margin-top: 30px; border-top: 5px solid var(--primary-color); }
.table-wrapper {
    overflow-x: auto;
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid var(--border-color);
    background: var(--card-background);
}
#bodyfat-history-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    font-size: 0.9rem;
    min-width: 700px; /* عرض ثابت لمنع التداخل */
    table-layout: fixed; /* تثبيت عرض الأعمدة لمنع التداخل */
}
#bodyfat-history-table th, #bodyfat-history-table td {
    border: 1px solid var(--border-color);
    padding: 8px 4px;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap; /* منع كسر النص في الخلايا */
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
}

/* تحديد عرض ثابت لكل عمود لمنع التداخل */
#bodyfat-history-table th:nth-child(1),
#bodyfat-history-table td:nth-child(1) {
    width: 110px; /* التاريخ */
    min-width: 110px;
    max-width: 110px;
}
#bodyfat-history-table th:nth-child(2),
#bodyfat-history-table td:nth-child(2) {
    width: 80px; /* الطريقة */
    min-width: 80px;
    max-width: 80px;
}
#bodyfat-history-table th:nth-child(3),
#bodyfat-history-table td:nth-child(3) {
    width: 90px; /* النسبة */
    min-width: 90px;
    max-width: 90px;
}
#bodyfat-history-table th:nth-child(4),
#bodyfat-history-table td:nth-child(4) {
    width: 110px; /* كتلة الدهون */
    min-width: 110px;
    max-width: 110px;
}
#bodyfat-history-table th:nth-child(5),
#bodyfat-history-table td:nth-child(5) {
    width: 110px; /* الكتلة الصافية */
    min-width: 110px;
    max-width: 110px;
}
#bodyfat-history-table th:nth-child(6),
#bodyfat-history-table td:nth-child(6) {
    width: 100px; /* التصنيف */
    min-width: 100px;
    max-width: 100px;
}
#bodyfat-history-table th:nth-child(7),
#bodyfat-history-table td:nth-child(7) {
    width: 60px; /* إجراء */
    min-width: 60px;
    max-width: 60px;
}

#bodyfat-history-table th {
    background-color: var(--primary-color);
    color: #fff;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}
#bodyfat-history-table tbody tr:nth-child(even) { background-color: var(--background-color); }
#bodyfat-history-table tbody tr:hover { background-color: var(--secondary-color); color: #fff; transition: background-color 0.2s ease; }
.delete-bf-log-btn {
    background: none;
    border: none;
    color: var(--delete-color);
    cursor: pointer;
    font-size: 1rem;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}
.delete-bf-log-btn:hover {
    color: #c62828;
    background-color: rgba(244, 67, 54, 0.1);
}
.chart-container {
    padding: 10px;
    background-color: var(--background-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

/* تحسينات إضافية للجوال */
.mobile-scroll-hint {
    display: none;
    text-align: center;
    font-size: 0.85rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    padding: 12px;
    background: linear-gradient(135deg, var(--card-background) 0%, var(--background-color) 100%);
    border-radius: 8px;
    border: 2px dashed var(--primary-color);
    font-weight: 600;
}
.mobile-scroll-hint i {
    margin-left: 8px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(0); }
    40% { transform: translateX(-5px); }
    60% { transform: translateX(5px); }
}

/* تحسين عرض التصنيفات */
.category-badge {
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90px;
    min-width: 60px;
}

/* تحسين أزرار الحذف */
.delete-bf-log-btn {
    min-width: 32px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسين التمرير الأفقي */
.table-wrapper::-webkit-scrollbar {
    height: 12px;
}

.table-wrapper::-webkit-scrollbar-track {
    background: var(--background-color);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.table-wrapper::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 6px;
    border: 2px solid var(--background-color);
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
}

/* تحسين التمرير للمتصفحات الأخرى */
.table-wrapper {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--background-color);
}

/* تحسينات عامة لضمان عدم تداخل البيانات */
#bodyfat-history-table {
    border-spacing: 0;
    border-collapse: collapse; /* استخدام collapse لضمان عدم التداخل */
}

#bodyfat-history-table th,
#bodyfat-history-table td {
    position: relative;
    box-sizing: border-box;
    border-right: 1px solid var(--border-color);
    border-left: 1px solid var(--border-color);
}

/* تحسين عرض البيانات الرقمية */
#bodyfat-history-table td:nth-child(3),
#bodyfat-history-table td:nth-child(4),
#bodyfat-history-table td:nth-child(5) {
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

/* تحسين عرض التصنيفات */
.category-badge {
    word-break: keep-all;
    white-space: nowrap;
}


@media (max-width: 768px) {
    .calculator-card { padding: 20px; }
    .calculator-card h3 { font-size: 1.3rem; }
    .shared-inputs-card .input-row, .method-section .input-row { flex-direction: column; gap: 15px; }
    .shared-inputs-card .input-row .input-group, .method-section .input-row .input-group { min-width: 100%; }
    .tab-button { font-size: 0.9rem; padding: 10px; }
    #bodyfat-result-display .result-item, #bodyfat-info-tips p, #bodyfat-info-tips ul li { font-size: 0.95rem; }

    /* تحسينات الجدول للتابلت */
    #bodyfat-history-table {
        font-size: 0.85rem;
        min-width: 650px; /* تقليل العرض قليلاً للتابلت */
    }
    #bodyfat-history-table th, #bodyfat-history-table td {
        padding: 6px 3px;
        font-size: 0.8rem;
    }
    /* تعديل عرض الأعمدة للتابلت */
    #bodyfat-history-table th:nth-child(1), #bodyfat-history-table td:nth-child(1) {
        width: 100px; min-width: 100px; max-width: 100px;
    }
    #bodyfat-history-table th:nth-child(2), #bodyfat-history-table td:nth-child(2) {
        width: 75px; min-width: 75px; max-width: 75px;
    }
    #bodyfat-history-table th:nth-child(3), #bodyfat-history-table td:nth-child(3) {
        width: 80px; min-width: 80px; max-width: 80px;
    }
    #bodyfat-history-table th:nth-child(4), #bodyfat-history-table td:nth-child(4) {
        width: 100px; min-width: 100px; max-width: 100px;
    }
    #bodyfat-history-table th:nth-child(5), #bodyfat-history-table td:nth-child(5) {
        width: 100px; min-width: 100px; max-width: 100px;
    }
    #bodyfat-history-table th:nth-child(6), #bodyfat-history-table td:nth-child(6) {
        width: 85px; min-width: 85px; max-width: 85px;
    }
    #bodyfat-history-table th:nth-child(7), #bodyfat-history-table td:nth-child(7) {
        width: 55px; min-width: 55px; max-width: 55px;
    }

    .table-wrapper {
        margin: 0 -15px; /* توسيع منطقة التمرير */
        padding: 0 15px;
    }
    .chart-container {
        margin: 20px -15px 0;
        padding: 15px;
    }
}

@media (max-width: 600px) {
    /* تحسينات إضافية للشاشات المتوسطة */
    .bodyfat-calculator-container { padding: 0 10px; }
    .calculator-card { padding: 15px; margin-bottom: 20px; }

    /* إظهار تلميح التمرير */
    .mobile-scroll-hint {
        display: block;
    }

    /* تحسين الجدول للشاشات المتوسطة */
    .table-wrapper {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin: 0 -15px;
        padding: 0 15px;
    }

    #bodyfat-history-table {
        min-width: 550px; /* عرض أدنى مناسب */
        font-size: 0.8rem;
        white-space: nowrap;
    }
    #bodyfat-history-table th, #bodyfat-history-table td {
        padding: 5px 2px;
        font-size: 0.75rem;
    }
    /* تعديل عرض الأعمدة للشاشات المتوسطة */
    #bodyfat-history-table th:nth-child(1), #bodyfat-history-table td:nth-child(1) {
        width: 90px; min-width: 90px; max-width: 90px;
    }
    #bodyfat-history-table th:nth-child(2), #bodyfat-history-table td:nth-child(2) {
        width: 65px; min-width: 65px; max-width: 65px;
    }
    #bodyfat-history-table th:nth-child(3), #bodyfat-history-table td:nth-child(3) {
        width: 70px; min-width: 70px; max-width: 70px;
    }
    #bodyfat-history-table th:nth-child(4), #bodyfat-history-table td:nth-child(4) {
        width: 90px; min-width: 90px; max-width: 90px;
    }
    #bodyfat-history-table th:nth-child(5), #bodyfat-history-table td:nth-child(5) {
        width: 90px; min-width: 90px; max-width: 90px;
    }
    #bodyfat-history-table th:nth-child(6), #bodyfat-history-table td:nth-child(6) {
        width: 75px; min-width: 75px; max-width: 75px;
    }
    #bodyfat-history-table th:nth-child(7), #bodyfat-history-table td:nth-child(7) {
        width: 50px; min-width: 50px; max-width: 50px;
    }

    /* تحسين عرض التصنيفات */
    .category-badge {
        font-size: 0.7rem;
        padding: 2px 4px;
        max-width: 60px;
        display: block;
        margin: 0 auto;
    }
    .delete-bf-log-btn {
        font-size: 0.8rem;
        padding: 2px;
        min-width: 26px;
        min-height: 26px;
    }
}

@media (max-width: 480px) {
    .bodyfat-calculator-container h2 { font-size: 1.5rem; }
    .tab-button { font-size: 0.8rem; padding: 8px; }
    #bodyfat-info-tips h4 { font-size: 1.1rem; }
    .calculator-card { padding: 12px; }

    /* تحسينات الجدول للجوال الصغير */
    .table-wrapper {
        margin: 0 -12px;
        padding: 0 12px;
        border-radius: 6px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    #bodyfat-history-table {
        min-width: 480px; /* عرض أدنى مناسب للجوال */
        font-size: 0.75rem;
        white-space: nowrap;
    }
    #bodyfat-history-table th, #bodyfat-history-table td {
        padding: 4px 2px;
        font-size: 0.7rem;
    }
    /* تعديل عرض الأعمدة للجوال الصغير */
    #bodyfat-history-table th:nth-child(1), #bodyfat-history-table td:nth-child(1) {
        width: 80px; min-width: 80px; max-width: 80px;
    }
    #bodyfat-history-table th:nth-child(2), #bodyfat-history-table td:nth-child(2) {
        width: 60px; min-width: 60px; max-width: 60px;
    }
    #bodyfat-history-table th:nth-child(3), #bodyfat-history-table td:nth-child(3) {
        width: 65px; min-width: 65px; max-width: 65px;
    }
    #bodyfat-history-table th:nth-child(4), #bodyfat-history-table td:nth-child(4) {
        width: 80px; min-width: 80px; max-width: 80px;
    }
    #bodyfat-history-table th:nth-child(5), #bodyfat-history-table td:nth-child(5) {
        width: 80px; min-width: 80px; max-width: 80px;
    }
    #bodyfat-history-table th:nth-child(6), #bodyfat-history-table td:nth-child(6) {
        width: 70px; min-width: 70px; max-width: 70px;
    }
    #bodyfat-history-table th:nth-child(7), #bodyfat-history-table td:nth-child(7) {
        width: 45px; min-width: 45px; max-width: 45px;
    }
    #bodyfat-history-table th {
        font-size: 0.72rem;
        font-weight: 600;
    }

    /* تحسين عرض البيانات في الخلايا */
    #bodyfat-history-table td {
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .category-badge {
        font-size: 0.6rem;
        padding: 1px 3px;
        border-radius: 6px;
        max-width: 50px;
        display: block;
        margin: 0 auto;
        text-align: center;
    }
    .delete-bf-log-btn {
        font-size: 0.7rem;
        padding: 1px;
        min-width: 24px;
        min-height: 24px;
    }
    .chart-container {
        margin: 15px -12px 0;
        padding: 10px;
        height: 250px;
    }
}

@media (max-width: 360px) {
    /* تحسينات للشاشات الصغيرة جداً */
    .bodyfat-calculator-container { padding: 0 5px; }
    .calculator-card { padding: 10px; }

    .table-wrapper {
        margin: 0 -10px;
        padding: 0 10px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    #bodyfat-history-table {
        min-width: 420px; /* عرض أدنى مناسب للشاشات الصغيرة */
        font-size: 0.7rem;
        white-space: nowrap;
    }
    #bodyfat-history-table th, #bodyfat-history-table td {
        padding: 3px 1px;
        font-size: 0.65rem;
        text-overflow: ellipsis;
        overflow: hidden;
    }
    /* تعديل عرض الأعمدة للشاشات الصغيرة جداً */
    #bodyfat-history-table th:nth-child(1), #bodyfat-history-table td:nth-child(1) {
        width: 70px; min-width: 70px; max-width: 70px;
    }
    #bodyfat-history-table th:nth-child(2), #bodyfat-history-table td:nth-child(2) {
        width: 55px; min-width: 55px; max-width: 55px;
    }
    #bodyfat-history-table th:nth-child(3), #bodyfat-history-table td:nth-child(3) {
        width: 60px; min-width: 60px; max-width: 60px;
    }
    #bodyfat-history-table th:nth-child(4), #bodyfat-history-table td:nth-child(4) {
        width: 70px; min-width: 70px; max-width: 70px;
    }
    #bodyfat-history-table th:nth-child(5), #bodyfat-history-table td:nth-child(5) {
        width: 70px; min-width: 70px; max-width: 70px;
    }
    #bodyfat-history-table th:nth-child(6), #bodyfat-history-table td:nth-child(6) {
        width: 60px; min-width: 60px; max-width: 60px;
    }
    #bodyfat-history-table th:nth-child(7), #bodyfat-history-table td:nth-child(7) {
        width: 40px; min-width: 40px; max-width: 40px;
    }

    .category-badge {
        font-size: 0.55rem;
        padding: 1px 2px;
        max-width: 45px;
        display: block;
        margin: 0 auto;
        text-align: center;
    }
    .delete-bf-log-btn {
        font-size: 0.65rem;
        min-width: 22px;
        min-height: 22px;
        padding: 1px;
    }
    .chart-container {
        height: 200px;
        margin: 10px -10px 0;
        padding: 8px;
    }

    /* تحسين عرض النصوص الطويلة */
    #bodyfat-history-table td {
        word-break: break-word;
        hyphens: auto;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    /* تحسين عرض التواريخ على الشاشات الصغيرة */
    #bodyfat-history-table td:first-child {
        font-size: 0.6rem;
        min-width: 60px;
    }

    /* تحسين عرض الأرقام */
    #bodyfat-history-table td:nth-child(3),
    #bodyfat-history-table td:nth-child(4),
    #bodyfat-history-table td:nth-child(5) {
        font-weight: 600;
        color: var(--primary-color);
    }
}