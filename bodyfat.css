/* bodyfat.css - Styles for Body Fat Percentage Calculator */

.bodyfat-calculator-container {
    max-width: 750px;
    margin: 0 auto;
}

.bodyfat-calculator-container h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.8rem;
}
.bodyfat-calculator-container h2 i { margin-left: 10px; }
.bodyfat-calculator-container .description {
    text-align: center;
    color: #888;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.calculator-card { /* Reused general card style */
    background-color: var(--card-background);
    padding: 25px 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
    margin-bottom: 30px;
}
.calculator-card h3 {
    text-align: center;
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 25px;
    font-size: 1.5rem;
}
.calculator-card h3 i { margin-left: 8px; }


.calculator-method-tabs {
    display: flex;
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--primary-color);
}
.tab-button {
    flex: 1;
    padding: 12px 15px;
    background-color: var(--card-background);
    color: var(--primary-color);
    border: none;
    cursor: pointer;
    font-size: 1.05rem;
    font-weight: 600;
    transition: background-color 0.3s, color 0.3s;
}
.tab-button:not(:last-child) {
    border-left: 1px solid var(--primary-color); /* For RTL, border-right */
}
.tab-button.active, .tab-button:hover {
    background-color: var(--primary-color);
    color: #fff;
}
[data-theme="dark"] .tab-button {
    background-color: var(--background-color);
    color: var(--secondary-color);
}
[data-theme="dark"] .tab-button.active, [data-theme="dark"] .tab-button:hover {
    background-color: var(--secondary-color);
    color: var(--background-color);
}

.method-section.hidden { display: none; }

.input-group { margin-bottom: 20px; }
.input-group label { display: block; margin-bottom: 8px; font-weight: 600; color: var(--text-color); }
.input-group input[type="number"] {
    width: 100%; padding: 12px 15px; border: 1px solid var(--border-color); border-radius: 8px;
    font-size: 1rem; background-color: var(--background-color); color: var(--text-color); font-family: 'Cairo', sans-serif;
}
.input-group input:focus { outline: none; border-color: var(--secondary-color); box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2); }

.gender-options { display: flex; border: 1px solid var(--border-color); border-radius: 8px; overflow: hidden; }
.gender-options input[type="radio"] { display: none; }
.gender-options label { flex: 1; padding: 12px; text-align: center; cursor: pointer; background-color: var(--background-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; font-size: 1rem; font-weight: 600; margin-bottom: 0; display: flex; align-items: center; justify-content: center; }
.gender-options label i { margin-left: 8px; }
.gender-options input[type="radio"]:checked + label { background-color: var(--primary-color); color: #fff; }
.gender-options label:first-of-type { border-left: 1px solid var(--border-color); }


#calculate-bf-navy-btn, #calculate-bf-bmi-btn {
    width: 100%; padding: 14px; border: none; border-radius: 8px; font-size: 1.1rem;
    cursor: pointer; transition: background-color 0.3s; font-weight: 600;
    display: flex; justify-content: center; align-items: center;
    background-color: var(--primary-color); color: #fff; margin-top: 10px;
}
#calculate-bf-navy-btn i, #calculate-bf-bmi-btn i { margin-left: 8px; }
#calculate-bf-navy-btn:hover, #calculate-bf-bmi-btn:hover { background-color: var(--secondary-color); }
.error-message { color: var(--delete-color); text-align: center; margin-top: 10px; font-weight: 600; min-height: 1em; }
.hidden { display: none !important; }

/* Results Display */
#bodyfat-result-display { border-top: 5px solid var(--success-color); }
#bodyfat-result-display .result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 5px;
    border-bottom: 1px solid var(--border-color);
    font-size: 1.1rem;
}
#bodyfat-result-display .result-item:last-of-type { border-bottom: none; } /* last direct child */
#bodyfat-result-display .result-item span { color: var(--text-color); }
#bodyfat-result-display .result-item strong { color: var(--primary-color); font-weight: 700; }

.category-badge {
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 1rem;
    color: #fff !important; /* Override strong color */
    font-weight: 600 !important;
}
/* Category specific colors - you can expand this */
.category-badge.athlete { background-color: #1976D2; } /* Blue */
.category-badge.fitness { background-color: #4CAF50; } /* Green */
.category-badge.average { background-color: #FFC107; color: #333 !important; } /* Amber */
.category-badge.obese { background-color: #f44336; } /* Red */
[data-theme="dark"] .category-badge.average { color: #000 !important; }


/* Info & Tips Section */
#bodyfat-info-tips { border-top: 5px solid var(--secondary-color); }
#bodyfat-info-tips p, #bodyfat-info-tips ul li {
    margin-bottom: 12px;
    line-height: 1.7;
    font-size: 1rem;
    color: var(--text-color);
}
#bodyfat-info-tips h4 {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-top: 20px;
    margin-bottom: 10px;
}
#bodyfat-info-tips ul {
    padding-right: 20px; /* Indent for RTL */
    list-style: disc;
}
#bodyfat-info-tips ul ul {
    margin-top: 5px;
    padding-right: 25px;
    list-style: circle;
}
.disclaimer { text-align: center; font-size: 0.9rem; color: #888; margin-top: 20px; font-style: italic; }


@media (max-width: 768px) {
    .calculator-card { padding: 20px; }
    .calculator-card h3 { font-size: 1.3rem; }
    .tab-button { font-size: 0.95rem; padding: 10px; }
    #bodyfat-result-display .result-item, #bodyfat-info-tips p, #bodyfat-info-tips ul li { font-size: 1rem; }
}
@media (max-width: 480px) {
    .bodyfat-calculator-container h2 { font-size: 1.5rem; }
    .tab-button { font-size: 0.85rem; }
    #bodyfat-result-display .result-item, #bodyfat-info-tips p, #bodyfat-info-tips ul li { font-size: 0.95rem; }
    #bodyfat-info-tips h4 { font-size: 1.1rem; }
}