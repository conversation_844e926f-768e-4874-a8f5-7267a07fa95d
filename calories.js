// calories.js

function initializeCaloriesPageModule() {
    console.log("Initializing Enhanced Calories Page Module...");

    const app = window.app;
    if (!app) { console.error("Main app object (window.app) not found!"); return; }

    // --- Get Elements ---
    const ageInput = document.getElementById('age');
    const weightInput = document.getElementById('weight-cal');
    const heightInput = document.getElementById('height-cal');
    const maleRadio = document.getElementById('male');
    const femaleRadio = document.getElementById('female');
    const activitySelect = document.getElementById('activity-level');
    const calculateBtn = document.getElementById('calculate-calories-btn');
    const errorMsg = document.getElementById('calories-error');
    const resultArea = document.getElementById('calories-result-area');
    const bmrResult = document.getElementById('bmr-result');
    const tdeeResult = document.getElementById('tdee-result');
    const lossResult = document.getElementById('loss-result');
    const gainResult = document.getElementById('gain-result');
    const macrosArea = document.getElementById('macros-result-area');
    const carbsResult = document.getElementById('carbs-result');
    const proteinResult = document.getElementById('protein-result');
    const fatResult = document.getElementById('fat-result');
    const tipsArea = document.getElementById('calorie-tips');
    const tipsList = document.getElementById('tips-list');
    const historyBody = document.getElementById('calorie-history-body');
    const metricRadio = document.getElementById('metric');
    const imperialRadio = document.getElementById('imperial');
    const weightLabel = document.getElementById('weight-label');
    const heightLabel = document.getElementById('height-label');
    const calorieGoalSelect = document.getElementById('calorie-goal');
    const macroRatioSelect = document.getElementById('macro-ratio');

    let currentResults = null; // Store current calculation results

    if (!calculateBtn || !historyBody) { console.error("Calories page core elements missing."); return; }
    if (calculateBtn.hasCalorieListener) { console.log("Calories listeners exist."); loadHistory(); return; }

    // --- Constants & Ratios ---
    const LBS_TO_KG = 0.453592;
    const INCH_TO_CM = 2.54;
    const MACRO_RATIOS = {
        balanced: { C: 0.40, P: 0.30, F: 0.30 },
        high_protein: { C: 0.30, P: 0.40, F: 0.30 },
        low_carb: { C: 0.25, P: 0.40, F: 0.35 }
    };

    // --- Helper & UI Functions ---
    const formatDateTime = (isoString) => new Date(isoString).toLocaleDateString('ar-EG');
    const updateLabels = () => {
        if (metricRadio.checked) {
            weightLabel.textContent = "الوزن (كجم):";
            heightLabel.textContent = "الطول (سم):";
            weightInput.placeholder = "70";
            heightInput.placeholder = "175";
        } else {
            weightLabel.textContent = "الوزن (باوند):";
            heightLabel.textContent = "الطول (إنش):";
            weightInput.placeholder = "154";
            heightInput.placeholder = "69";
        }
    };

    // --- Calculation Functions ---
    const calculateBMR = (weightKg, heightCm, age, gender) => {
        let bmr = (10 * weightKg) + (6.25 * heightCm) - (5 * age);
        bmr += (gender === 'male' ? 5 : -161);
        return Math.round(bmr);
    };

    const calculateMacros = () => {
        if (!currentResults) return;

        const goal = calorieGoalSelect.value;
        const ratioKey = macroRatioSelect.value;
        const ratio = MACRO_RATIOS[ratioKey];
        let targetCalories = 0;

        switch (goal) {
            case 'loss': targetCalories = currentResults.lossGoal; break;
            case 'gain': targetCalories = currentResults.gainGoal; break;
            default: targetCalories = currentResults.tdee; break;
        }

        carbsResult.textContent = Math.round((targetCalories * ratio.C) / 4);
        proteinResult.textContent = Math.round((targetCalories * ratio.P) / 4);
        fatResult.textContent = Math.round((targetCalories * ratio.F) / 9);
    };

    const generateTips = (tdee, lossGoal) => {
        tipsList.innerHTML = '';
        let tips = [];
        tips.push({ icon: 'fa-balance-scale', text: `سعرات الحفاظ على وزنك هي حوالي ${tdee}.` });
        if (tdee < 1800) {
            tips.push({ icon: 'fa-utensils', text: 'قد تحتاج لزيادة السعرات قليلاً إذا كنت تشعر بالتعب، ركز على الأطعمة الصحية.' });
        }
        tips.push({ icon: 'fa-weight', text: `لخسارة نصف كيلو أسبوعياً، استهدف حوالي ${lossGoal} سعر حراري مع ممارسة الرياضة.` });
        tips.push({ icon: 'fa-tint', text: 'لا تنس شرب كميات كافية من الماء على مدار اليوم.' });
        tips.push({ icon: 'fa-dumbbell', text: 'إضافة تمارين القوة تساعد في بناء العضلات ورفع معدل الحرق.' });
        tips.push({ icon: 'fa-bed', text: 'الحصول على قسط كافٍ من النوم ضروري للصحة العامة وتنظيم الوزن.' });

        tips.forEach(tip => {
            const li = document.createElement('li');
            li.innerHTML = `<i class="fas ${tip.icon}"></i> ${tip.text}`;
            tipsList.appendChild(li);
        });
        tipsArea.classList.remove('hidden');
    };

    // --- DB & Table Functions ---
    const handleDeleteClick = async (id, rowElement) => { /* ... (Same as before) ... */
        if (!confirm("هل أنت متأكد؟")) return;
        try {
            await app.deleteData(app.calorieStoreName, id);
            rowElement.remove(); app.showToast("تم حذف السجل.");
            if (historyBody.rows.length === 0) { historyBody.innerHTML = '<tr><td colspan="5" style="text-align:center;">لا يوجد سجل.</td></tr>'; }
        } catch (e) { console.error(e); alert("فشل الحذف."); }
    };

    const addCalorieRowToTable = (record) => {
        const row = document.createElement('tr');
        row.setAttribute('data-id', record.id);
        const weightDisplay = record.units === 'imperial' ? `${Math.round(record.weight * 2.20462)} باوند` : `${record.weight} كجم`;
        const goalDisplay = record.lossGoal ? `${record.lossGoal} (خسارة)` : `${record.tdee} (حفاظ)`; // Simplified goal display
        row.innerHTML = `
            <td>${formatDateTime(record.date)}</td>
            <td>${weightDisplay}</td>
            <td>${record.tdee}</td>
            <td>${goalDisplay}</td>
            <td><button class="delete-row-btn" title="حذف"><i class="fas fa-trash-alt"></i></button></td>
        `;
        row.querySelector('.delete-row-btn').addEventListener('click', () => handleDeleteClick(Number(record.id), row));
        const placeholderRow = historyBody.querySelector('td[colspan="5"]');
        if (placeholderRow) placeholderRow.parentElement.remove();
        historyBody.prepend(row);
    };

    const loadHistory = async () => { /* ... (Similar, calls addCalorieRowToTable) ... */
        historyBody.innerHTML = '<tr><td colspan="5" style="text-align:center;">يتم تحميل السجل...</td></tr>';
        try {
            const records = await app.getAllData(app.calorieStoreName);
            historyBody.innerHTML = '';
            if (records && records.length > 0) {
                records.sort((a, b) => new Date(b.date) - new Date(a.date));
                records.forEach(addCalorieRowToTable);
            } else {
                historyBody.innerHTML = '<tr><td colspan="5" style="text-align:center;">لا يوجد سجل.</td></tr>';
            }
        } catch (error) { historyBody.innerHTML = '<tr><td colspan="5" style="text-align:center; color:red;">فشل التحميل.</td></tr>'; }
    };

    // --- Main Calculation Handler ---
    const handleCalculateAndSave = async () => {
        errorMsg.textContent = '';
        const age = parseInt(ageInput.value, 10);
        let weight = parseFloat(weightInput.value);
        let height = parseFloat(heightInput.value);
        const gender = maleRadio.checked ? 'male' : 'female';
        const activityMultiplier = parseFloat(activitySelect.value);
        const units = metricRadio.checked ? 'metric' : 'imperial';

        if (!age || !weight || !height || age <= 0 || weight <= 0 || height <= 0) {
            errorMsg.textContent = 'الرجاء إدخال جميع القيم بشكل صحيح.'; return;
        }

        let weightKg = weight;
        let heightCm = height;

        if (units === 'imperial') {
            weightKg = weight * LBS_TO_KG;
            heightCm = height * INCH_TO_CM;
        }

        const bmr = calculateBMR(weightKg, heightCm, age, gender);
        const tdee = Math.round(bmr * activityMultiplier);
        const lossGoal = tdee - 500;
        const gainGoal = tdee + 500;

        currentResults = { bmr, tdee, lossGoal, gainGoal }; // Store results for macro calc

        bmrResult.textContent = bmr;
        tdeeResult.textContent = tdee;
        lossResult.textContent = lossGoal;
        gainResult.textContent = gainGoal;

        calculateMacros(); // Calculate and display macros
        generateTips(tdee, lossGoal); // Generate and display tips

        resultArea.classList.remove('hidden');

        // --- Save to DB ---
        const recordToSave = {
            age, weight: Math.round(weightKg), height: Math.round(heightCm), gender, activityMultiplier, units,
            bmr, tdee, lossGoal, gainGoal,
            date: new Date().toISOString()
        };

        try {
            const newId = await app.saveData(app.calorieStoreName, recordToSave);
            recordToSave.id = newId;
            addCalorieRowToTable(recordToSave);
            app.showToast("تم الحساب والحفظ بنجاح!");
        } catch (error) {
            console.error("Failed to save calorie record:", error);
            alert("حدث خطأ أثناء حفظ السجل.");
        }
    };

    // --- Attach Listeners ---
    metricRadio.addEventListener('change', updateLabels);
    imperialRadio.addEventListener('change', updateLabels);
    calorieGoalSelect.addEventListener('change', calculateMacros);
    macroRatioSelect.addEventListener('change', calculateMacros);
    calculateBtn.addEventListener('click', handleCalculateAndSave);
    calculateBtn.hasCalorieListener = true;

    // --- Initial Setup ---
    updateLabels(); // Set initial labels
    loadHistory();
    console.log("Enhanced Calories Page Module Initialized.");
}

// --- Set the global initializer ---
window.currentPageInitializer = initializeCaloriesPageModule;