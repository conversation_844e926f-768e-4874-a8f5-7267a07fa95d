// activity.js

function initializeActivityPageModule() {
    console.log("Initializing Enhanced Physical Activity Tracker Module...");
    const app = window.app;
    if (!app) { console.error("Main app object (window.app) not found!"); return; }

    // --- DOM Elements ---
    const activityDateInput = document.getElementById('activity-date');
    const currentWeightInput = document.getElementById('current-weight-activity');
    const activityTypeSelect = document.getElementById('activity-type');
    const activityTypeFilterInput = document.getElementById('activity-type-filter');
    const activityDurationInput = document.getElementById('activity-duration');
    const activityIntensitySelect = document.getElementById('activity-intensity');
    const activityDistanceInput = document.getElementById('activity-distance');
    const activityNotesInput = document.getElementById('activity-notes');
    const manualCaloriesToggle = document.getElementById('manual-calories-toggle');
    const manualCaloriesInputGroup = document.getElementById('manual-calories-input-group');
    const manualCaloriesBurnedInput = document.getElementById('manual-calories-burned');
    const logActivityBtn = document.getElementById('log-activity-btn');
    const updateActivityBtn = document.getElementById('update-activity-btn');
    const cancelEditActivityBtn = document.getElementById('cancel-edit-activity-btn');
    const activityFormTitle = document.getElementById('activity-form-title');
    const activityErrorMsg = document.getElementById('activity-error');

    const logDatePicker = document.getElementById('log-activity-date-picker');
    const summaryDateSpan = document.getElementById('activity-summary-date');
    const totalDurationSpan = document.getElementById('total-activity-duration');
    const totalCaloriesBurnedSpan = document.getElementById('total-calories-burned');
    const activityHistoryListUl = document.getElementById('activity-history-list');

    const weeklyMinutesGoalInput = document.getElementById('weekly-minutes-goal');
    const weeklyCaloriesGoalInput = document.getElementById('weekly-calories-goal');
    const setActivityGoalsBtn = document.getElementById('set-activity-goals-btn');
    const currentWeeklyGoalsDisplay = document.getElementById('current-weekly-goals-display');
    
    // ** هذا هو المتغير الذي يسبب الخطأ إذا لم يتم العثور على العنصر **
    const weeklyActivityStatsContent = document.getElementById('weekly-activity-stats-content');
    
    const weeklyTotalMinutesSpan = document.getElementById('weekly-total-minutes');
    const weeklyMinutesGoalDisplaySpan = document.getElementById('weekly-minutes-goal-display');
    const weeklyMinutesProgress = document.getElementById('weekly-minutes-progress');
    const weeklyTotalCaloriesSpan = document.getElementById('weekly-total-calories');
    const weeklyCaloriesGoalDisplaySpan = document.getElementById('weekly-calories-goal-display');
    const weeklyCaloriesProgress = document.getElementById('weekly-calories-progress');
    const weeklyHighestCaloriesSpan = document.getElementById('weekly-highest-calories');
    const weeklyLowestCaloriesSpan = document.getElementById('weekly-lowest-calories');

    const weeklyChartCanvas = document.getElementById('activityWeeklyChart');
    const activityChartError = document.getElementById('activity-chart-error');

    let currentlyEditingActivityId = null;
    let activityChartInstance = null;
    let userWeightKg = 70;
    let activityGoals = { weeklyMinutes: 150, weeklyCalories: 1500 };

    // ** التحقق من وجود العناصر الأساسية **
    if (!logActivityBtn || !activityHistoryListUl || !weeklyChartCanvas || !setActivityGoalsBtn || !weeklyActivityStatsContent) {
        console.error("Activity tracker essential elements (including weeklyActivityStatsContent) missing. Initialization failed.");
        // يمكنك عرض رسالة خطأ للمستخدم في الواجهة هنا إذا أردت
        const mainTrackerContainer = document.querySelector('.activity-tracker-container');
        if(mainTrackerContainer) mainTrackerContainer.innerHTML = `<p class="error-message" style="padding:20px; text-align:center;">حدث خطأ في تحميل مكونات الصفحة. الرجاء المحاولة مرة أخرى أو الاتصال بالدعم.</p>`;
        return;
    }

    if (logActivityBtn.hasActivityListener) {
        console.log("Activity tracker listeners already exist. Re-initializing data...");
        loadUserWeight().then(() => loadActivityGoals()).then(() => {
            loadLogForDate(logDatePicker.value || formatDate(new Date()));
            loadWeeklySummaryAndChart();
        });
        return;
    }

    const MET_VALUES = [ /* ... (نفس قائمة METs من الرد السابق) ... */
        { name: "اختر نشاطًا...", value: 0, id: "" }, { name: "المشي (ترفيهي، < 3.2 كم/س)", value: 2.0, id: "walk_slow" }, { name: "المشي (3.2-4.5 كم/س، بوتيرة معتدلة)", value: 3.5, id: "walk_moderate" }, { name: "المشي (4.8-5.5 كم/س، بوتيرة نشطة)", value: 4.3, id: "walk_brisk" }, { name: "المشي (صعودًا، 5% انحدار)", value: 6.0, id: "walk_uphill" }, { name: "الهرولة (عام)", value: 7.0, id: "jog_general" }, { name: "الركض (8 كم/س)", value: 8.3, id: "run_8kmh" }, { name: "الركض (9.7 كم/س)", value: 9.8, id: "run_9_7kmh" }, { name: "الركض (12 كم/س)", value: 11.8, id: "run_12kmh" }, { name: "ركوب الدراجة (< 16 كم/س، ترفيهي)", value: 4.0, id: "bike_leisure" }, { name: "ركوب الدراجة (19-22 كم/س، معتدل)", value: 8.0, id: "bike_moderate" }, { name: "ركوب الدراجة (> 25 كم/س، شاق/سباق)", value: 12.0, id: "bike_vigorous" }, { name: "السباحة (عام، بوتيرة معتدلة)", value: 7.0, id: "swim_moderate" }, { name: "السباحة (زحف على البطن، سريع)", value: 10.0, id: "swim_crawl_fast" }, { name: "تمارين القوة (عام، مجهود متوسط)", value: 3.5, id: "strength_moderate" }, { name: "تمارين القوة (مجهود شاق، أوزان ثقيلة)", value: 6.0, id: "strength_vigorous" }, { name: "كرة السلة (مباراة)", value: 8.0, id: "basketball_game" }, { name: "كرة القدم (مباراة تنافسية)", value: 10.0, id: "soccer_competitive" }, { name: "كرة الطائرة (مباراة تنافسية)", value: 4.0, id: "volleyball_competitive" }, { name: "التنس (فردي)", value: 7.3, id: "tennis_singles" }, { name: "أعمال المنزل (تنظيف خفيف)", value: 2.3, id: "housework_light" }, { name: "أعمال المنزل (تنظيف شاق، كنس، مسح)", value: 3.8, id: "housework_heavy" }, { name: "البستنة (عام)", value: 3.8, id: "gardening_general" }, { name: "اليوغا (هاتا)", value: 2.5, id: "yoga_hatha" }, { name: "القفز بالحبل", value: 11.0, id: "jumping_rope" },
    ];

    const formatDate = (d) => new Date(d).toISOString().split('T')[0];
    const escapeHtml = (unsafe) => unsafe.replace(/[&<"']/g, (match) => ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;',"'":'&#039;'})[match]);

    const populateActivitySelect = (filterText = "") => { /* ... (نفسها) ... */
        const currentVal = activityTypeSelect.value; activityTypeSelect.innerHTML = '<option value="0" disabled selected>-- اختر نشاطًا --</option>';
        MET_VALUES.filter(act => act.value > 0 && act.name.toLowerCase().includes(filterText.toLowerCase()))
                  .forEach(activity => { const option = document.createElement('option'); option.value = activity.value; option.textContent = activity.name; option.dataset.activityId = activity.id; activityTypeSelect.appendChild(option); });
        if (currentVal) activityTypeSelect.value = currentVal;
    };
    if(activityTypeFilterInput) activityTypeFilterInput.addEventListener('input', (e) => populateActivitySelect(e.target.value));

    const loadUserWeight = async () => { /* ... (نفسها) ... */
        try {
            const weightSetting = await app.getUserProfileSetting('currentUserWeight');
            if (weightSetting && weightSetting.value) userWeightKg = parseFloat(weightSetting.value);
            else { const calorieRecords = await app.getAllData(app.calorieStoreName); if (calorieRecords && calorieRecords.length > 0) userWeightKg = parseFloat(calorieRecords.sort((a,b)=>new Date(b.date)-new Date(a.date))[0].weight);
                   else { const bmiRecords = await app.getAllData(app.bmiStoreName); if (bmiRecords && bmiRecords.length > 0) userWeightKg = parseFloat(bmiRecords.sort((a,b)=>new Date(b.date)-new Date(a.date))[0].weight); }
            }
        } catch (e) { console.error("Error loading weight", e); }
        if(currentWeightInput) currentWeightInput.value = userWeightKg || '';
        if (!userWeightKg || userWeightKg <= 0) userWeightKg = 70;
    };
    if(currentWeightInput) currentWeightInput.addEventListener('change', () => { const newWeight = parseFloat(currentWeightInput.value); if (!isNaN(newWeight) && newWeight > 0) { userWeightKg = newWeight; app.putUserProfileSetting({ setting: 'currentUserWeight', value: userWeightKg }); }});

    const loadActivityGoals = async () => { /* ... (نفسها) ... */
        try { const goalsSetting = await app.getUserProfileSetting('activityGoals'); if (goalsSetting && goalsSetting.value) activityGoals = goalsSetting.value; else activityGoals = { weeklyMinutes: 150, weeklyCalories: 1500 };}
        catch (e) { console.error("Error loading activity goals", e); activityGoals = { weeklyMinutes: 150, weeklyCalories: 1500 }; }
        if(weeklyMinutesGoalInput) weeklyMinutesGoalInput.value = activityGoals.weeklyMinutes;
        if(weeklyCaloriesGoalInput) weeklyCaloriesGoalInput.value = activityGoals.weeklyCalories;
        if(currentWeeklyGoalsDisplay) currentWeeklyGoalsDisplay.innerHTML = `<p>الدقائق: <strong>${activityGoals.weeklyMinutes}</strong> د/أسبوع | السعرات: <strong>${activityGoals.weeklyCalories}</strong> سعر/أسبوع</p>`;
    };
    const handleSetActivityGoals = async () => { /* ... (نفسها) ... */
        const minutes = parseInt(weeklyMinutesGoalInput.value, 10); const calories = parseInt(weeklyCaloriesGoalInput.value, 10);
        if ((isNaN(minutes) || minutes < 0) && (isNaN(calories) || calories < 0)) { app.showToast("أدخل أهدافًا صحيحة."); return;}
        activityGoals = { weeklyMinutes: minutes || 0, weeklyCalories: calories || 0 };
        try { await app.putUserProfileSetting({ setting: 'activityGoals', value: activityGoals }); app.showToast("تم حفظ الأهداف!"); loadActivityGoals(); loadWeeklySummaryAndChart(); }
        catch (e) { app.showToast("فشل حفظ الأهداف."); }
    };
    if(setActivityGoalsBtn) setActivityGoalsBtn.addEventListener('click', handleSetActivityGoals);

    if(manualCaloriesToggle) manualCaloriesToggle.addEventListener('change', () => { if(manualCaloriesInputGroup) manualCaloriesInputGroup.classList.toggle('hidden', !manualCaloriesToggle.checked); });

    const calculateCaloriesBurned = (met, duration) => { /* ... (نفسها) ... */ if(!met || !duration || !userWeightKg || userWeightKg <=0) return 0; return Math.round((met * userWeightKg * 3.5) / 200 * duration); };
    const updateDailyActivitySummary = (activities) => { /* ... (نفسها) ... */
        let totalDuration = 0; let totalCalories = 0; activities.forEach(act => { totalDuration += act.duration || 0; totalCalories += act.caloriesBurned || 0; });
        if(totalDurationSpan) totalDurationSpan.textContent = totalDuration; if(totalCaloriesBurnedSpan) totalCaloriesBurnedSpan.textContent = totalCalories;
    };
    const addActivityRowToTable = (act) => { /* ... (نفسها) ... */
        const li = document.createElement('li'); li.setAttribute('data-activity-id', act.id);
        let detailsHtml = `<p class="activity-item-details">المدة: <span>${act.duration} دقيقة</span>`;
        if(act.intensity) detailsHtml += `, الشدة: <span>${escapeHtml(act.intensity)}</span>`; if(act.distance) detailsHtml += `, المسافة: <span>${act.distance} كم</span>`;
        detailsHtml += `, <span class="calories">السعرات: ~${act.caloriesBurned}</span>`; if(act.isManualEntry) detailsHtml += `<span class="manual-entry-flag">(يدوي)</span>`; detailsHtml += `</p>`;
        if(act.notes) detailsHtml += `<p class="activity-item-notes">${escapeHtml(act.notes)}</p>`;
        li.innerHTML = `<div class="activity-item-header"><strong>${escapeHtml(act.activityName)}</strong></div> ${detailsHtml} <div class="activity-item-actions"><button class="edit-activity-btn" title="تعديل"><i class="fas fa-edit"></i></button><button class="delete-activity-btn" title="حذف"><i class="fas fa-trash-alt"></i></button></div>`;
        li.querySelector('.delete-activity-btn').addEventListener('click', () => deleteActivityLog(act.id, li)); li.querySelector('.edit-activity-btn').addEventListener('click', () => populateFormForEdit(act));
        const placeholder = activityHistoryListUl.querySelector('.no-entries'); if (placeholder) placeholder.remove(); activityHistoryListUl.prepend(li);
    };
    const loadLogForDate = async (dateStr) => { /* ... (نفسها) ... */
        if(!dateStr) dateStr = formatDate(new Date()); if(summaryDateSpan) summaryDateSpan.textContent = new Date(dateStr + 'T00:00:00').toLocaleDateString('ar-EG', {weekday:'long', year:'numeric', month:'long', day:'numeric'});
        try { const acts = await app.getDataByIndex(app.activityLogStoreName, 'date', dateStr); if(activityHistoryListUl) activityHistoryListUl.innerHTML = ''; if(acts.length === 0 && activityHistoryListUl) activityHistoryListUl.innerHTML = '<li class="no-entries">لم يتم تسجيل أي أنشطة لهذا اليوم.</li>'; else acts.sort((a,b)=> new Date(b.timestamp) - new Date(a.timestamp)).forEach(addActivityRowToTable); updateDailyActivitySummary(acts); }
        catch (e) { console.error(e); if(activityHistoryListUl) activityHistoryListUl.innerHTML = '<li class="no-entries" style="color:red;">خطأ تحميل السجل.</li>'; updateDailyActivitySummary([]); }
    };

    const resetActivityForm = () => { /* ... (نفسها) ... */
        if(activityTypeSelect) activityTypeSelect.value = ""; if(activityTypeFilterInput) activityTypeFilterInput.value = ""; populateActivitySelect();
        if(activityDurationInput) activityDurationInput.value = ''; if(activityIntensitySelect) activityIntensitySelect.value = ""; if(activityDistanceInput) activityDistanceInput.value = '';
        if(activityNotesInput) activityNotesInput.value = ''; if(activityDateInput) activityDateInput.value = formatDate(new Date());
        if(manualCaloriesToggle) manualCaloriesToggle.checked = false; if(manualCaloriesInputGroup) manualCaloriesInputGroup.classList.add('hidden'); if(manualCaloriesBurnedInput) manualCaloriesBurnedInput.value = '';
        if(activityErrorMsg) activityErrorMsg.textContent = ''; if(logActivityBtn) logActivityBtn.classList.remove('hidden');
        if(updateActivityBtn) updateActivityBtn.classList.add('hidden'); if(cancelEditActivityBtn) cancelEditActivityBtn.classList.add('hidden');
        if(activityFormTitle) activityFormTitle.innerHTML = '<i class="fas fa-plus-circle"></i> تسجيل نشاط جديد'; currentlyEditingActivityId = null;
    };
    const populateFormForEdit = (act) => { /* ... (نفسها) ... */
        currentlyEditingActivityId = act.id; if(activityDateInput) activityDateInput.value = act.date; if(currentWeightInput) currentWeightInput.value = userWeightKg; populateActivitySelect();
        if(activityTypeSelect) {const selectedOpt = Array.from(activityTypeSelect.options).find(o => parseFloat(o.value) === act.activityMET && o.textContent === act.activityName); if(selectedOpt) activityTypeSelect.value = selectedOpt.value; else activityTypeSelect.value = "";}
        if(activityDurationInput) activityDurationInput.value = act.duration; if(activityIntensitySelect) activityIntensitySelect.value = act.intensity || "";
        if(activityDistanceInput) activityDistanceInput.value = act.distance || ""; if(activityNotesInput) activityNotesInput.value = act.notes || "";
        if(manualCaloriesToggle) manualCaloriesToggle.checked = act.isManualEntry || false; if(manualCaloriesInputGroup) manualCaloriesInputGroup.classList.toggle('hidden', !manualCaloriesToggle.checked); if(manualCaloriesBurnedInput) manualCaloriesBurnedInput.value = act.isManualEntry ? act.caloriesBurned : '';
        if(activityFormTitle) activityFormTitle.innerHTML = '<i class="fas fa-edit"></i> تعديل النشاط'; if(logActivityBtn) logActivityBtn.classList.add('hidden');
        if(updateActivityBtn) updateActivityBtn.classList.remove('hidden'); if(cancelEditActivityBtn) cancelEditActivityBtn.classList.remove('hidden'); if(activityTypeSelect) activityTypeSelect.focus();
    };
    if(cancelEditActivityBtn) cancelEditActivityBtn.addEventListener('click', resetActivityForm);

    const handleLogOrUpdateActivity = async (isUpdate = false) => { /* ... (نفسها) ... */
        if(activityErrorMsg) activityErrorMsg.textContent = ''; await loadUserWeight();
        const date = activityDateInput.value || formatDate(new Date()); const selectedOption = activityTypeSelect.options[activityTypeSelect.selectedIndex];
        const activityName = selectedOption ? selectedOption.textContent : "نشاط غير محدد"; const activityMET = parseFloat(activityTypeSelect.value);
        const duration = parseInt(activityDurationInput.value, 10); const intensity = activityIntensitySelect.value;
        const distance = parseFloat(activityDistanceInput.value) || null; const notes = activityNotesInput.value.trim();
        const isManualEntry = manualCaloriesToggle.checked; let caloriesBurned = 0;
        if ((!activityMET || activityMET <= 0) && !isManualEntry) { if(activityErrorMsg) activityErrorMsg.textContent = "اختر نشاطًا أو أدخل السعرات يدويًا."; return; }
        if (!duration || duration <= 0) { if(activityErrorMsg) activityErrorMsg.textContent = "أدخل مدة صحيحة."; return; }
        if (userWeightKg <= 0 && !isManualEntry) { if(activityErrorMsg) activityErrorMsg.textContent = "أدخل وزن صحيح."; if(currentWeightInput) currentWeightInput.focus(); return; }
        if (isManualEntry) { caloriesBurned = parseInt(manualCaloriesBurnedInput.value, 10); if (isNaN(caloriesBurned) || caloriesBurned < 0) { if(activityErrorMsg) activityErrorMsg.textContent = "أدخل سعرات يدوية صحيحة."; return; } }
        else { caloriesBurned = calculateCaloriesBurned(activityMET, duration); }
        const entry = { date, activityName, activityMET: isManualEntry ? null : activityMET, duration, intensity, distance, notes, caloriesBurned, isManualEntry, timestamp: new Date().toISOString() };
        try { if (isUpdate && currentlyEditingActivityId) { entry.id = currentlyEditingActivityId; await app.putData(app.activityLogStoreName, entry); app.showToast("تم تحديث النشاط!"); } else { await app.saveData(app.activityLogStoreName, entry); app.showToast("تم تسجيل النشاط!"); }
            resetActivityForm(); await loadLogForDate(logDatePicker.value); await loadWeeklySummaryAndChart();
        } catch (e) { console.error("Error saving activity:", e); if(activityErrorMsg) activityErrorMsg.textContent = 'خطأ حفظ النشاط.'; }
    };
    if(logActivityBtn) logActivityBtn.addEventListener('click', () => handleLogOrUpdateActivity(false));
    if(updateActivityBtn) updateActivityBtn.addEventListener('click', () => handleLogOrUpdateActivity(true));

    const deleteActivityLog = async (id, el) => { /* ... (نفسها) ... */
        if(!confirm("متأكد من الحذف؟")) return; try{await app.deleteData(app.activityLogStoreName, id); el.remove(); app.showToast("تم الحذف."); await loadLogForDate(logDatePicker.value); await loadWeeklySummaryAndChart();} catch(e){console.error(e); alert("فشل الحذف.");}
    };

    const renderWeeklyActivityChart = (chartData) => { /* ... (نفسها) ... */
        if(!weeklyChartCanvas) return; const ctx = weeklyChartCanvas.getContext('2d'); if(activityChartInstance) activityChartInstance.destroy();
        const isDark = document.body.dataset.theme === 'dark'; const gridC = isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'; const labelC = isDark ? '#ccc' : '#555';
        const barC = 'rgba(255, 159, 64, 0.7)'; const borderC = 'rgba(255, 159, 64, 1)';
        activityChartInstance = new Chart(ctx, { type: 'bar', data: { labels: chartData.labels, datasets: [{ label: 'السعرات المحروقة', data: chartData.caloriesData, backgroundColor: barC, borderColor: borderC, borderWidth: 1 }] }, options: { responsive: true, maintainAspectRatio: false, scales: { y: { beginAtZero: true, title: {display:true, text:'السعرات المحروقة', color:labelC}, ticks:{color:labelC}, grid:{color:gridC}}, x: {title: {display:true, text:'التاريخ', color:labelC}, ticks:{color:labelC}, grid:{display:false}} }, plugins: { legend:{labels:{color:labelC}}, tooltip:{titleFont:{family:'Cairo'}, bodyFont:{family:'Cairo'}}} } });
    };

    const loadWeeklySummaryAndChart = async () => { /* ... (نفسها، تأكد من أن weeklyActivityStatsContent يُستخدم بشكل صحيح) ... */
        // ** التحقق هنا **
        if (!weeklyActivityStatsContent || !weeklyTotalMinutesSpan || !weeklyMinutesGoalDisplaySpan || !weeklyMinutesProgress ||
            !weeklyTotalCaloriesSpan || !weeklyCaloriesGoalDisplaySpan || !weeklyCaloriesProgress ||
            !weeklyHighestCaloriesSpan || !weeklyLowestCaloriesSpan || !activityChartError || !weeklyChartCanvas) {
            console.error("One or more weekly stats/chart DOM elements are missing in `loadWeeklySummaryAndChart`.");
            return;
        }
        activityChartError.classList.add('hidden');
        weeklyActivityStatsContent.innerHTML = '<p class="loading-stats">يتم تحميل ملخص الأسبوع...</p>';
        try { await loadActivityGoals(); const today = new Date(); let totalMinutesWeek = 0, totalCaloriesWeek = 0, highestCals = 0, lowestCals = Infinity, daysWithActivity = 0; const chartLabels = [], chartCaloriesData = [];
            for (let i = 6; i >= 0; i--) { const date = new Date(today); date.setDate(today.getDate() - i); const dateString = formatDate(date); chartLabels.push(date.toLocaleDateString('ar-EG', { day: 'numeric', month: 'short' })); const activities = await app.getDataByIndex(app.activityLogStoreName, 'date', dateString); let dailyCalories = 0, dailyMinutes = 0; if(activities.length > 0) daysWithActivity++; activities.forEach(act => { dailyMinutes += act.duration; dailyCalories += act.caloriesBurned; }); totalMinutesWeek += dailyMinutes; totalCaloriesWeek += dailyCalories; if(dailyCalories > 0) { if(dailyCalories > highestCals) highestCals = dailyCalories; if(dailyCalories < lowestCals) lowestCals = dailyCalories; } chartCaloriesData.push(dailyCalories); }
            if (lowestCals === Infinity && daysWithActivity > 0) lowestCals = highestCals; else if (daysWithActivity === 0) lowestCals = 0;
            weeklyTotalMinutesSpan.textContent = totalMinutesWeek; weeklyMinutesGoalDisplaySpan.textContent = activityGoals.weeklyMinutes || '--'; weeklyMinutesProgress.style.width = `${activityGoals.weeklyMinutes > 0 ? Math.min((totalMinutesWeek / activityGoals.weeklyMinutes) * 100, 100) : 0}%`;
            weeklyTotalCaloriesSpan.textContent = totalCaloriesWeek; weeklyCaloriesGoalDisplaySpan.textContent = activityGoals.weeklyCalories || '--'; weeklyCaloriesProgress.style.width = `${activityGoals.weeklyCalories > 0 ? Math.min((totalCaloriesWeek / activityGoals.weeklyCalories) * 100, 100) : 0}%`;
            weeklyHighestCaloriesSpan.textContent = highestCals; weeklyLowestCaloriesSpan.textContent = lowestCals;
            // Ensure the parent div is cleared before re-populating with specific elements
            weeklyActivityStatsContent.innerHTML = `
                <p>إجمالي الدقائق: <strong id="weekly-total-minutes">${totalMinutesWeek}</strong> / <span id="weekly-minutes-goal-display">${activityGoals.weeklyMinutes || '--'}</span> دقيقة</p>
                <div class="progress-bar-container small-progress"><div class="progress-bar" id="weekly-minutes-progress" style="width: ${activityGoals.weeklyMinutes > 0 ? Math.min((totalMinutesWeek / activityGoals.weeklyMinutes) * 100, 100) : 0}%;"></div></div>
                <p>إجمالي السعرات المحروقة: <strong id="weekly-total-calories">${totalCaloriesWeek}</strong> / <span id="weekly-calories-goal-display">${activityGoals.weeklyCalories || '--'}</span> سعر</p>
                <div class="progress-bar-container small-progress"><div class="progress-bar protein-bar" id="weekly-calories-progress" style="width: ${activityGoals.weeklyCalories > 0 ? Math.min((totalCaloriesWeek / activityGoals.weeklyCalories) * 100, 100) : 0}%;"></div></div>
                <p>أعلى سعرات محروقة في يوم: <strong id="weekly-highest-calories">${highestCals}</strong> سعر</p>
                <p>أقل سعرات محروقة في يوم (مع تسجيل): <strong id="weekly-lowest-calories">${lowestCals}</strong> سعر</p>
            `;
            if (!daysWithActivity) { activityChartError.textContent = "لا توجد بيانات نشاط لعرضها للـ 7 أيام الماضية."; activityChartError.classList.remove('hidden'); if (activityChartInstance) activityChartInstance.destroy(); weeklyChartCanvas.style.display = 'none'; }
            else { weeklyChartCanvas.style.display = 'block'; renderWeeklyActivityChart({ labels: chartLabels, caloriesData: chartCaloriesData }); }
        } catch (e) { console.error("Error loading weekly activity stats/chart:", e); weeklyActivityStatsContent.innerHTML = '<p style="color:red;">خطأ تحميل الإحصائيات.</p>'; activityChartError.textContent = "خطأ تحميل بيانات الرسم البياني."; activityChartError.classList.remove('hidden'); }
    };

    const initializePage = async () => {
        populateActivitySelect();
        const todayStr = formatDate(new Date());
        if(activityDateInput) activityDateInput.value = todayStr;
        if(logDatePicker) logDatePicker.value = todayStr;
        await loadUserWeight();
        await loadActivityGoals();
        await loadLogForDate(todayStr);
        await loadWeeklySummaryAndChart();
        resetActivityForm();
    };

    if(logDatePicker) logDatePicker.addEventListener('change', (e) => loadLogForDate(e.target.value));
    initializePage();
    if(logActivityBtn) logActivityBtn.hasActivityListener = true;
    console.log("Enhanced Physical Activity Tracker Module Initialized.");
}
window.currentPageInitializer = initializeActivityPageModule;