// bodyfat.js

function initializeBodyFatPageModule() {
    console.log("Initializing Enhanced Body Fat Calculator Module (Final Attempt at Syntax)...");
    const app = window.app;
    if (!app) {
        console.error("Main app object (window.app) not found! BodyFatPage cannot initialize.");
        return;
    }

    // --- DOM Elements ---
    const metricRadio = document.getElementById('bf-metric');
    const imperialRadio = document.getElementById('bf-imperial');
    const navyMethodTab = document.querySelector('.tab-button[data-method="us-navy"]');
    const bmiFormulaTab = document.querySelector('.tab-button[data-method="bmi-formula"]');
    const usNavySection = document.getElementById('us-navy-method');
    const bmiFormulaSection = document.getElementById('bmi-formula-method');
    const ageSharedInput = document.getElementById('bf-age-shared');
    const heightSharedInput = document.getElementById('bf-height-shared');
    const weightSharedInput = document.getElementById('bf-weight-shared');
    const heightLabelShared = document.getElementById('bf-height-label-shared');
    const weightLabelShared = document.getElementById('bf-weight-label-shared');
    const bfGenderNavyRadios = document.querySelectorAll('input[name="bf-gender-navy"]');
    const bfMaleNavyRadio = document.getElementById('bf-male-navy');
    const bfFemaleNavyRadio = document.getElementById('bf-female-navy');
    const bfNeckNavyInput = document.getElementById('bf-neck-navy');
    const bfWaistNavyInput = document.getElementById('bf-waist-navy');
    const bfHipNavyGroup = document.getElementById('bf-hip-navy-group');
    const bfHipNavyInput = document.getElementById('bf-hip-navy');
    const calculateNavyBtn = document.getElementById('calculate-bf-navy-btn');
    const bfNavyError = document.getElementById('bf-navy-error');
    const bfNeckLabelNavy = document.getElementById('bf-neck-label-navy');
    const bfWaistLabelNavy = document.getElementById('bf-waist-label-navy');
    const bfHipLabelNavy = document.getElementById('bf-hip-label-navy');
    const bfGenderBmiRadios = document.querySelectorAll('input[name="bf-gender-bmi"]');
    const bfMaleBmiRadio = document.getElementById('bf-male-bmi');
    const calculateBmiFormulaBtn = document.getElementById('calculate-bf-bmi-btn');
    const bfBmiError = document.getElementById('bf-bmi-error');
    const resultDisplayArea = document.getElementById('bodyfat-result-display');
    const bfMethodUsedSpan = document.getElementById('bf-method-used');
    const bfPercentageResultSpan = document.getElementById('bf-percentage-result');
    const fatMassResultSpan = document.getElementById('fat-mass-result');
    const leanMassResultSpan = document.getElementById('lean-mass-result');
    const bfCategoryResultSpan = document.getElementById('bf-category-result');
    const infoTipsArea = document.getElementById('bodyfat-info-tips');
    const tipsCategoryTitle = document.getElementById('tips-category-title');
    const categorySpecificTipsDiv = document.getElementById('category-specific-tips');
    const historyTableBody = document.getElementById('bodyfat-history-body');
    const chartCanvas = document.getElementById('bodyfatProgressChart');
    const chartErrorMsg = document.getElementById('bf-chart-error');
    let bodyFatChartInstance = null;

    if (!navyMethodTab || !calculateNavyBtn || !calculateBmiFormulaBtn || !resultDisplayArea || !historyTableBody || !chartCanvas || !bfFemaleNavyRadio ) {
        console.error("Body fat calculator essential DOM elements missing. Init failed.");
        const mainBfContainer = document.querySelector('.bodyfat-calculator-container');
        if(mainBfContainer) { mainBfContainer.innerHTML = `<p class="error-message" style="padding:20px; text-align:center;">خطأ تحميل مكونات الصفحة.</p>`; }
        return;
    }

    if (calculateNavyBtn.hasBfListener) {
        console.log("Body fat listeners appear to be already attached. Re-initializing data if needed.");
        loadInitialData();
        return;
    }

    const KG_TO_LBS = 2.20462;
    const CM_TO_INCHES = 0.393701;
    const LBS_TO_KG = 1 / KG_TO_LBS;
    const INCHES_TO_CM = 1 / CM_TO_INCHES;
    let currentUnits = 'metric';

    const formatDate = (dateObj) => {
        const d = dateObj instanceof Date ? dateObj : new Date(dateObj);
        const month = ('' + (d.getMonth() + 1)).padStart(2, '0');
        const day = ('' + d.getDate()).padStart(2, '0');
        const year = d.getFullYear();
        return `${year}-${month}-${day}`;
    };

    const escapeHtml = (unsafe) => {
        if (typeof unsafe !== 'string') {
            return unsafe === null || typeof unsafe === 'undefined' ? '' : String(unsafe);
        }
        return unsafe.replace(/[&<"']/g, (match) => ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;',"'":'&#039;'})[match]);
    };

    const updateInputLabelsAndPlaceholders = () => { /* ... (الكود كما هو) ... */ };
    const handleUnitChange = () => { currentUnits = metricRadio.checked ? 'metric' : 'imperial'; updateInputLabelsAndPlaceholders(); };
    if(metricRadio) { metricRadio.addEventListener('change', handleUnitChange); }
    if(imperialRadio) { imperialRadio.addEventListener('change', handleUnitChange); }
    const prefillSharedInputs = async () => { /* ... (الكود كما هو) ... */ };
    const switchMethodTab = (method) => { /* ... (الكود كما هو) ... */ };
    if(navyMethodTab) { navyMethodTab.addEventListener('click', () => switchMethodTab('us-navy')); }
    if(bmiFormulaTab) { bmiFormulaTab.addEventListener('click', () => switchMethodTab('bmi-formula')); }
    const toggleHipInputNavy = () => { if (bfFemaleNavyRadio && bfHipNavyGroup) { bfHipNavyGroup.classList.toggle('hidden', !bfFemaleNavyRadio.checked); } };
    if(bfGenderNavyRadios) { bfGenderNavyRadios.forEach(radio => radio.addEventListener('change', toggleHipInputNavy)); }
    const getInputsAsMetric = (methodPrefix = "bf", includeHip = false) => { /* ... (الكود كما هو) ... */
        const age = ageSharedInput ? parseFloat(ageSharedInput.value) : null; let height = heightSharedInput ? parseFloat(heightSharedInput.value) : null;
        let weight = weightSharedInput ? parseFloat(weightSharedInput.value) : null; let neck = (methodPrefix === 'bf-navy' && bfNeckNavyInput) ? parseFloat(bfNeckNavyInput.value) : null;
        let waist = (methodPrefix === 'bf-navy' && bfWaistNavyInput) ? parseFloat(bfWaistNavyInput.value) : null;
        let hip = (methodPrefix === 'bf-navy' && includeHip && bfHipNavyInput) ? parseFloat(bfHipNavyInput.value) : null;
        if (currentUnits === 'imperial') { if(height)height*=INCHES_TO_CM; if(weight)weight*=LBS_TO_KG; if(neck)neck*=INCHES_TO_CM; if(waist)waist*=INCHES_TO_CM; if(hip)hip*=INCHES_TO_CM; }
        return { age, heightCm: height, weightKg: weight, neckCm: neck, waistCm: waist, hipCm: hip };
    };
    const calculateUSNavyBodyFat = () => { /* ... (الكود كما هو) ... */
        if(bfNavyError) { bfNavyError.textContent = ''; } const genderRadio = document.querySelector('input[name="bf-gender-navy"]:checked');
        if(!genderRadio) { if(bfNavyError) { bfNavyError.textContent = "الرجاء تحديد الجنس."; } return null;} const gender = genderRadio.value;
        const { heightCm, neckCm, waistCm, hipCm } = getInputsAsMetric('bf-navy', gender === 'female');
        if (!heightCm || !neckCm || !waistCm || heightCm <= 0 || neckCm <=0 || waistCm <=0) { if(bfNavyError) { bfNavyError.textContent = "أدخل قيمًا صحيحة وموجبة للطول، الرقبة، والخصر."; } return null;}
        if (gender === 'female' && (!hipCm || hipCm <=0)) { if(bfNavyError) { bfNavyError.textContent = "للإناث، أدخلي قيمة صحيحة وموجبة للورك."; } return null;}
        if (gender === 'male' && neckCm >= waistCm ) { if(bfNavyError) { bfNavyError.textContent = "للرجال: محيط الرقبة يجب أن يكون أصغر من الخصر."; } return null; }
        if (gender === 'female' && neckCm >= (waistCm + hipCm)) { if(bfNavyError) { bfNavyError.textContent = "للنساء: مجموع الخصر والورك يجب أن يكون أكبر من الرقبة."; } return null; }
        let bfp;
        if (gender === 'male') { bfp = 495 / (1.0324 - 0.19077 * Math.log10(waistCm - neckCm) + 0.15456 * Math.log10(heightCm)) - 450; }
        else { bfp = 495 / (1.29579 - 0.35004 * Math.log10(waistCm + hipCm - neckCm) + 0.22100 * Math.log10(heightCm)) - 450; }
        if (isNaN(bfp) || bfp < 2 || bfp > 60) { if(bfNavyError) { bfNavyError.textContent = "القيم غير واقعية. تأكد من دقة القياسات."; } return null; } return bfp;
    };
    const calculateBMIFormulaBodyFat = () => { /* ... (الكود كما هو) ... */
        if(bfBmiError) { bfBmiError.textContent = ''; } const genderRadio = document.querySelector('input[name="bf-gender-bmi"]:checked');
        if(!genderRadio) { if(bfBmiError) { bfBmiError.textContent = "الرجاء تحديد الجنس."; } return null;} const gender = genderRadio.value;
        const { age, heightCm, weightKg } = getInputsAsMetric('bf-bmi');
        if (!heightCm || !weightKg || !age || heightCm <=0 || weightKg <=0 || age <=0) { if(bfBmiError) { bfBmiError.textContent = "أدخل قيمًا صحيحة وموجبة للطول، الوزن، والعمر."; } return null; }
        const bmi = weightKg / ((heightCm/100) * (heightCm/100)); let bfp;
        if (age >= 18) { bfp = (1.20 * bmi) + (0.23 * age) - (10.8 * (gender === 'male' ? 1 : 0)) - 5.4; }
        else { bfp = (1.51 * bmi) - (0.70 * age) - (3.6 * (gender === 'male' ? 1 : 0)) + 1.4; }
        if (isNaN(bfp) || bfp < 2 || bfp > 60) { if(bfBmiError) { bfBmiError.textContent = "القيم المدخلة غير واقعية."; } return null; } return bfp;
    };

    // ** الدالة التي قد تكون سبب المشكلة - تم تبسيط منطق دمج النصائح **
    const getBodyFatCategoryAndTips = (bfp, gender) => {
        let categoryText = "غير محدد";
        let categoryClassName = "";
        let specificTips = ["الحفاظ على نسبة دهون صحية مهم للصحة العامة."]; // تبدأ بنصيحة افتراضية

        const commonTips = [
            "اتبع نظامًا غذائيًا متوازنًا غنيًا بالبروتين والألياف والدهون الصحية.",
            "مارس التمارين الرياضية بانتظام، واجمع بين تمارين القوة (الأوزان) والكارديو.",
            "احصل على قسط كافٍ من النوم والراحة (7-9 ساعات للبالغين).",
            "اشرب كميات كافية من الماء على مدار اليوم.",
            "تجنب التوتر قدر الإمكان وحاول التحكم فيه بتقنيات الاسترخاء."
        ];

        if (gender === 'male') {
            if (bfp < 6) { categoryText = "منخفض جداً (ضروري)"; categoryClassName = "athlete"; specificTips = ["هذا المستوى قد يكون منخفضًا جدًا لغير الرياضيين المحترفين ويحتاج لمتابعة غذائية دقيقة لضمان الحصول على الطاقة الكافية."]; }
            else if (bfp <= 13) { categoryText = "رياضي"; categoryClassName = "athlete"; specificTips = ["مستوى ممتاز للرياضيين، حافظ على نظامك التدريبي والغذائي المتوازن."]; }
            else if (bfp <= 17) { categoryText = "لائق"; categoryClassName = "fitness"; specificTips = ["مستوى جيد جدًا للصحة واللياقة، استمر في الحفاظ على نشاطك البدني وتغذيتك الجيدة."]; }
            else if (bfp <= 24) { categoryText = "متوسط/مقبول"; categoryClassName = "average"; specificTips = ["ضمن النطاق المقبول لمعظم البالغين، يمكن تحسينه بزيادة طفيفة في النشاط البدني وبعض التعديلات الغذائية الصحية."]; }
            else { categoryText = "سمنة"; categoryClassName = "obese"; specificTips = ["هذا المستوى يزيد من مخاطر بعض الأمراض. ينصح بمراجعة مختص لوضع خطة مناسبة وصحية لتقليل نسبة الدهون."]; }
        } else { // female
            if (bfp < 14) { categoryText = "منخفض جداً (ضروري)"; categoryClassName = "athlete"; specificTips = ["هذا المستوى قد يكون منخفضًا جدًا وقد يؤثر على الوظائف الهرمونية والصحة العامة، تأكدي من حصولك على تغذية كافية واستشيري مختصًا."]; }
            else if (bfp <= 20) { categoryText = "رياضية"; categoryClassName = "athlete"; specificTips = ["مستوى ممتاز للرياضيات، حافظي على نظامك."]; }
            else if (bfp <= 24) { categoryText = "لائقة"; categoryClassName = "fitness"; tips = ["مستوى جيد جدًا للصحة واللياقة، استمري في الحفاظ على لياقتك."]; } // خطأ إملائي محتمل هنا: tips بدلاً من specificTips
            else if (bfp <= 31) { categoryText = "متوسط/مقبول"; categoryClassName = "average"; specificTips = ["ضمن النطاق المقبول لمعظم البالغات، يمكن تحسينه بزيادة طفيفة في النشاط البدني وبعض التعديلات الغذائية الصحية."]; }
            else { categoryText = "سمنة"; categoryClassName = "obese"; specificTips = ["هذا المستوى يزيد من مخاطر بعض الأمراض. ينصح بمراجعة مختص لوضع خطة مناسبة وصحية لتقليل نسبة الدهون."]; }
        }

        // تصحيح الخطأ الإملائي المحتمل:
        if (gender === 'female' && bfp > 20 && bfp <= 24 && typeof tips !== 'undefined') { // التحقق من tips
            specificTips = tips; // استخدام tips إذا تم تعيينها بالخطأ
        }


        // دمج النصائح مع التأكد من عدم التكرار
        let combinedTips = Array.isArray(specificTips) ? [...specificTips] : [specificTips]; // تأكد أن specificTips مصفوفة
        commonTips.forEach(commonTip => {
            if (!combinedTips.includes(commonTip)) {
                combinedTips.push(commonTip);
            }
        });

        return { text: categoryText, className: categoryClassName, tips: combinedTips };
    };

    const displayResults = async (bfp, method, inputs) => { /* ... (بقية الدالة كما كانت في الرد السابق، مع التأكد من أن استدعاء getBodyFatCategoryAndTips صحيح) ... */
        if (bfp === null || isNaN(bfp)) { if(resultDisplayArea) { resultDisplayArea.classList.add('hidden'); } if(infoTipsArea) { infoTipsArea.classList.add('hidden'); } return; }
        if (!resultDisplayArea || !bfPercentageResultSpan || !bfMethodUsedSpan || !fatMassResultSpan || !leanMassResultSpan || !bfCategoryResultSpan || !infoTipsArea || !tipsCategoryTitle || !categorySpecificTipsDiv) { console.error("Result display DOM elements missing."); return; }
        const bodyFatPercentage = parseFloat(bfp.toFixed(1)); bfPercentageResultSpan.textContent = `${bodyFatPercentage} %`; bfMethodUsedSpan.textContent = method==='us-navy'?"البحرية الأمريكية":"BMI المعدلة";
        let fatMassKg=0, leanMassKg=0; const weightKgForMass = inputs.weightKg;
        if(weightKgForMass && weightKgForMass > 0) { fatMassKg=parseFloat(((bodyFatPercentage/100)*weightKgForMass).toFixed(1)); leanMassKg=parseFloat((weightKgForMass-fatMassKg).toFixed(1)); fatMassResultSpan.textContent=`${fatMassKg} كجم`; leanMassResultSpan.textContent=`${leanMassKg} كجم`; if(fatMassResultSpan.parentElement)fatMassResultSpan.parentElement.classList.remove('hidden'); if(leanMassResultSpan.parentElement)leanMassResultSpan.parentElement.classList.remove('hidden'); }
        else { if(fatMassResultSpan.parentElement)fatMassResultSpan.parentElement.classList.add('hidden'); if(leanMassResultSpan.parentElement)leanMassResultSpan.parentElement.classList.add('hidden');}
        const categoryInfo = getBodyFatCategoryAndTips(bodyFatPercentage, inputs.gender); bfCategoryResultSpan.textContent=categoryInfo.text; bfCategoryResultSpan.className=`category-badge ${categoryInfo.className}`;
        tipsCategoryTitle.textContent=`لـ (${categoryInfo.text})`; categorySpecificTipsDiv.innerHTML=`<ul>${categoryInfo.tips.map(tip=>`<li>${escapeHtml(tip)}</li>`).join('')}</ul>`;
        resultDisplayArea.classList.remove('hidden'); infoTipsArea.classList.remove('hidden'); resultDisplayArea.scrollIntoView({behavior:'smooth',block:'center'});
        const logEntry={date:formatDate(new Date()),method,bfp:bodyFatPercentage,fatMassKg:(weightKgForMass>0)?fatMassKg:null,leanMassKg:(weightKgForMass>0)?leanMassKg:null,category:categoryInfo.text,inputs,timestamp:new Date().toISOString()};
        try{await app.saveData(app.bodyFatLogStoreName,logEntry);app.showToast("تم حفظ النتيجة!");loadBodyFatHistoryAndChart();}
        catch(error){console.error("Failed to save BFP log:",error);app.showToast("فشل حفظ النتيجة.");}
    };

    // بقية الدوال (calculateNavyBtn listener, calculateBmiFormulaBtn listener, addBodyFatRowToTable, renderBodyFatChart, loadBodyFatHistoryAndChart, loadInitialData) تبقى كما هي من الرد السابق
    // مع التأكد من أن جميع الفواصل المنقوطة موجودة في أماكنها الصحيحة وأن الأقواس متطابقة.
    // ... (الكود الطويل لهذه الدوال - سأفترض أنه صحيح بناءً على الاختبارات السابقة لهذه الدوال تحديدًا)

    if(calculateNavyBtn) { calculateNavyBtn.addEventListener('click', () => { const genderRadio = document.querySelector('input[name="bf-gender-navy"]:checked'); if(!genderRadio) { if(bfNavyError) bfNavyError.textContent = "حدد الجنس."; return;} const gender = genderRadio.value; const inputs = getInputsAsMetric('bf-navy', gender === 'female'); inputs.gender = gender; inputs.units = currentUnits; const bfp = calculateUSNavyBodyFat(); displayResults(bfp, "us-navy", inputs); }); }
    if(calculateBmiFormulaBtn) { calculateBmiFormulaBtn.addEventListener('click', () => { const genderRadio = document.querySelector('input[name="bf-gender-bmi"]:checked'); if(!genderRadio) { if(bfBmiError) bfBmiError.textContent = "حدد الجنس."; return;} const gender = genderRadio.value; const inputs = getInputsAsMetric('bf-bmi'); inputs.gender = gender; inputs.units = currentUnits; const bfp = calculateBMIFormulaBodyFat(); displayResults(bfp, "bmi-formula", inputs); }); }

    const addBodyFatRowToTable = (record) => {
        if(!historyTableBody) { return; } const row = historyTableBody.insertRow(0); row.setAttribute('data-id', record.id);
        const categoryInfo = getBodyFatCategoryAndTips(record.bfp, record.inputs.gender); // Re-get category info for class
        const categoryClass = categoryInfo ? categoryInfo.className : '';
        row.innerHTML = `<td>${new Date(record.date).toLocaleDateString('ar-EG')}</td><td>${record.method==='us-navy'?'البحرية':'BMI'}</td><td>${record.bfp.toFixed(1)}%</td><td>${record.fatMassKg !== null ? record.fatMassKg.toFixed(1)+' كجم' : '--'}</td><td>${record.leanMassKg !== null ? record.leanMassKg.toFixed(1)+' كجم' : '--'}</td><td><span class="category-badge ${categoryClass}">${record.category}</span></td><td><button class="delete-bf-log-btn" title="حذف"><i class="fas fa-times-circle"></i></button></td>`;
        const deleteBtn = row.querySelector('.delete-bf-log-btn');
        if(deleteBtn) { deleteBtn.addEventListener('click', async () => { if (confirm("متأكد؟")) { try { await app.deleteData(app.bodyFatLogStoreName, record.id); row.remove(); app.showToast("تم الحذف."); if (historyTableBody.rows.length === 0) historyTableBody.innerHTML = `<tr><td colspan="7" style="text-align:center;">لا يوجد سجل.</td></tr>`; loadBodyFatHistoryAndChart(); } catch (err) { app.showToast("فشل الحذف.");}}});}
    };
const renderBodyFatChart = (historyData) => {
        if (!chartCanvas) {
            console.warn("Chart canvas not found for bodyfat chart.");
            return;
        }
        const ctx = chartCanvas.getContext('2d');
        if (bodyFatChartInstance) {
            bodyFatChartInstance.destroy(); // Destroy previous instance
        }

        // --- التأكد من أن البيانات مرتبة حسب التاريخ ---
        historyData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

        const labels = historyData.map(r => new Date(r.date).toLocaleDateString('ar-EG', { month: 'short', day: 'numeric' }));
        const bfpData = historyData.map(r => r.bfp);

        const isDarkMode = document.body.dataset.theme === 'dark';
        const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
        const labelColor = isDarkMode ? '#e0e0e0' : '#444';
        const lineColor = 'rgba(0, 150, 136, 1)'; // لون تركوازي غني (Teal)
        const pointBackgroundColor = lineColor;
        const pointBorderColor = isDarkMode ? '#1e1e1e' : '#ffffff'; // لون حدود النقطة يعتمد على الوضع
        const tooltipBgColor = isDarkMode ? 'rgba(30, 30, 30, 0.9)' : 'rgba(255, 255, 255, 0.95)';
        const tooltipTitleColor = isDarkMode ? '#00bfa5' : '#00796b'; // لون عنوان التلميح
        const tooltipBodyColor = isDarkMode ? '#e0e0e0' : '#333';

        // --- إنشاء تدرج لوني للملء تحت الخط ---
        const gradient = ctx.createLinearGradient(0, 0, 0, 300); // 300 هو ارتفاع تقديري للرسم
        gradient.addColorStop(0, 'rgba(0, 150, 136, 0.6)'); // لون البدء (أقوى)
        gradient.addColorStop(1, 'rgba(0, 150, 136, 0.05)'); // لون الانتهاء (شفاف)

        bodyFatChartInstance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'نسبة الدهون (%)',
                    data: bfpData,
                    borderColor: lineColor,
                    backgroundColor: gradient, // استخدام التدرج اللوني
                    fill: true,
                    tension: 0.4, // زيادة نعومة الخط
                    pointBackgroundColor: pointBackgroundColor,
                    pointBorderColor: pointBorderColor,
                    pointBorderWidth: 2, // زيادة سماكة حدود النقطة
                    pointRadius: 5, // حجم النقطة
                    pointHoverRadius: 8, // حجم النقطة عند المرور
                    pointHoverBorderWidth: 3,
                    pointHoverBackgroundColor: pointBorderColor, // عكس الألوان عند المرور
                    pointHoverBorderColor: pointBackgroundColor,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: { // تحسين التفاعل
                    mode: 'index',
                    intersect: false,
                },
                animation: { // إضافة حركة بسيطة
                    duration: 1000,
                    easing: 'easeInOutQuart',
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        title: { display: true, text: 'نسبة الدهون (%)', color: labelColor, font: { family: 'Cairo', size: 14, weight: '600' } },
                        ticks: {
                            color: labelColor,
                            font: { family: 'Cairo' },
                            padding: 10, // إضافة حشوة
                            callback: function(value) {
                                return parseFloat(value).toFixed(1) + '%';
                            }
                        },
                        grid: { color: gridColor, drawBorder: false } // إخفاء حدود المحور
                    },
                    x: {
                        title: { display: false }, // لا حاجة لعنوان المحور السيني
                        ticks: {
                            color: labelColor,
                            font: { family: 'Cairo' },
                            maxRotation: 0, // منع دوران العناوين
                            autoSkip: true, // السماح بتخطي بعض العناوين لتجنب الازدحام
                            maxTicksLimit: 7 // تحديد أقصى عدد للعناوين
                        },
                        grid: { display: false } // إخفاء خطوط الشبكة للمحور السيني
                    }
                },
                plugins: {
                    legend: {
                        display: false // لا حاجة لها لخط واحد
                    },
                    tooltip: { // --- تحسينات على التلميحات (Tooltips) ---
                        enabled: true,
                        backgroundColor: tooltipBgColor,
                        titleColor: tooltipTitleColor,
                        bodyColor: tooltipBodyColor,
                        borderColor: lineColor,
                        borderWidth: 1,
                        titleFont: { family: 'Cairo', weight: 'bold', size: 14 },
                        bodyFont: { family: 'Cairo', size: 12 },
                        padding: 12, // زيادة الحشوة
                        displayColors: false, // إخفاء مربع اللون
                        cornerRadius: 8, // حواف دائرية
                        boxPadding: 4,
                        callbacks: {
                            title: function(tooltipItems) {
                                // عرض التاريخ الكامل في العنوان
                                const index = tooltipItems[0].dataIndex;
                                const date = new Date(historyData[index].date);
                                return date.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' });
                            },
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) { label += ': '; }
                                if (context.parsed.y !== null) {
                                    label += parseFloat(context.parsed.y).toFixed(1) + '%';
                                }
                                // --- إضافة التصنيف إلى التلميح ---
                                const bfp = context.parsed.y;
                                const record = historyData[context.dataIndex];
                                // التأكد من وجود البيانات اللازمة لحساب التصنيف
                                if (record && record.inputs && record.inputs.gender && typeof getBodyFatCategoryAndTips === 'function') {
                                    const categoryInfo = getBodyFatCategoryAndTips(bfp, record.inputs.gender);
                                    return [label, `التصنيف: ${categoryInfo.text}`]; // عرض النسبة والتصنيف
                                }
                                return label; // عرض النسبة فقط إذا لم تتوفر بيانات التصنيف
                            }
                        }
                    }
                }
            }
        });
    };

    const loadBodyFatHistoryAndChart = async () => {
        if(!historyTableBody || !chartErrorMsg || !chartCanvas) { return; }
        historyTableBody.innerHTML = '<tr><td colspan="7" style="text-align:center;">يتم التحميل...</td></tr>'; chartErrorMsg.classList.add('hidden');
        try { const records = await app.getAllData(app.bodyFatLogStoreName); historyTableBody.innerHTML = '';
            if (records && records.length > 0) { records.sort((a,b)=>new Date(b.timestamp)-new Date(a.timestamp)).forEach(addBodyFatRowToTable); const chartRecs = records.slice().sort((a,b)=>new Date(a.date).getTime()-new Date(b.date).getTime()).slice(-30); renderBodyFatChart(chartRecs); chartCanvas.style.display='block';}
            else { historyTableBody.innerHTML = `<tr><td colspan="7" style="text-align:center;">لا يوجد سجل.</td></tr>`; chartErrorMsg.textContent = "لا توجد بيانات للرسم."; chartErrorMsg.classList.remove('hidden'); if(bodyFatChartInstance){bodyFatChartInstance.destroy(); bodyFatChartInstance=null;} chartCanvas.style.display='none';}
        } catch (e) { historyTableBody.innerHTML = `<tr><td colspan="7" style="color:red;">فشل تحميل السجل.</td></tr>`; chartErrorMsg.textContent = "خطأ تحميل بيانات الرسم."; chartErrorMsg.classList.remove('hidden');}
    };

    async function loadInitialData() {
        updateInputLabelsAndPlaceholders();
        await prefillSharedInputs();
        toggleHipInputNavy();
        if(bfMaleNavyRadio) { bfMaleNavyRadio.checked = true; }
        if(bfMaleBmiRadio) { bfMaleBmiRadio.checked = true; }
        switchMethodTab('us-navy');
        await loadBodyFatHistoryAndChart();
    }

    loadInitialData();
    if(calculateNavyBtn) { calculateNavyBtn.hasBfListener = true; } // وضع علامة التهيئة
    console.log("Enhanced Body Fat Calculator Module Initialized Successfully.");
}

// --- هذا السطر يجب أن يكون موجودًا وفي نهاية الملف ---
window.currentPageInitializer = initializeBodyFatPageModule;