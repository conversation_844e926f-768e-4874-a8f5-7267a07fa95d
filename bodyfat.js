// bodyfat.js

function initializeBodyFatPageModule() {
    console.log("Initializing Body Fat Percentage Calculator Module...");
    const app = window.app; // Assuming window.app is available for future use (e.g., saving)
    // if (!app) { console.error("Main app object (window.app) not found!"); return; }

    // --- DOM Elements ---
    const navyMethodTab = document.querySelector('.tab-button[data-method="us-navy"]');
    const bmiFormulaTab = document.querySelector('.tab-button[data-method="bmi-formula"]');
    const usNavySection = document.getElementById('us-navy-method');
    const bmiFormulaSection = document.getElementById('bmi-formula-method');

    // US Navy Method Elements
    const bfMaleNavyRadio = document.getElementById('bf-male-navy');
    const bfFemaleNavyRadio = document.getElementById('bf-female-navy');
    const bfHeightNavyInput = document.getElementById('bf-height-navy');
    const bfNeckNavyInput = document.getElementById('bf-neck-navy');
    const bfWaistNavyInput = document.getElementById('bf-waist-navy');
    const bfHipNavyGroup = document.getElementById('bf-hip-navy-group');
    const bfHipNavyInput = document.getElementById('bf-hip-navy');
    const calculateNavyBtn = document.getElementById('calculate-bf-navy-btn');
    const bfNavyError = document.getElementById('bf-navy-error');

    // BMI Formula Method Elements
    const bfMaleBmiRadio = document.getElementById('bf-male-bmi');
    // const bfFemaleBmiRadio = document.getElementById('bf-female-bmi'); // Not strictly needed if one is checked
    const bfHeightBmiInput = document.getElementById('bf-height-bmi');
    const bfWeightBmiInput = document.getElementById('bf-weight-bmi');
    const bfAgeBmiInput = document.getElementById('bf-age-bmi');
    const calculateBmiFormulaBtn = document.getElementById('calculate-bf-bmi-btn');
    const bfBmiError = document.getElementById('bf-bmi-error');

    // Results Display Elements
    const resultDisplayArea = document.getElementById('bodyfat-result-display');
    const bfPercentageResultSpan = document.getElementById('bf-percentage-result');
    const fatMassResultSpan = document.getElementById('fat-mass-result');
    const leanMassResultSpan = document.getElementById('lean-mass-result');
    const bfCategoryResultSpan = document.getElementById('bf-category-result');
    const infoTipsArea = document.getElementById('bodyfat-info-tips');


    if (!navyMethodTab || !calculateNavyBtn || !calculateBmiFormulaBtn || !resultDisplayArea) {
        console.error("Body fat calculator essential elements missing. Initialization failed.");
        return;
    }
    if (calculateNavyBtn.hasBfListener) { // Check if already initialized
        console.log("Body fat calculator listeners already exist.");
        return;
    }

    // --- Tab Switching Logic ---
    const switchMethodTab = (method) => {
        if (method === 'us-navy') {
            usNavySection.classList.add('active');
            usNavySection.classList.remove('hidden');
            bmiFormulaSection.classList.add('hidden');
            bmiFormulaSection.classList.remove('active');
            navyMethodTab.classList.add('active');
            bmiFormulaTab.classList.remove('active');
        } else { // bmi-formula
            bmiFormulaSection.classList.add('active');
            bmiFormulaSection.classList.remove('hidden');
            usNavySection.classList.add('hidden');
            usNavySection.classList.remove('active');
            bmiFormulaTab.classList.add('active');
            navyMethodTab.classList.remove('active');
        }
        resultDisplayArea.classList.add('hidden'); // Hide results when switching tabs
        infoTipsArea.classList.add('hidden');
    };

    navyMethodTab.addEventListener('click', () => switchMethodTab('us-navy'));
    bmiFormulaTab.addEventListener('click', () => switchMethodTab('bmi-formula'));

    // --- US Navy Specific Logic ---
    const toggleHipInput = () => {
        bfHipNavyGroup.classList.toggle('hidden', !bfFemaleNavyRadio.checked);
    };
    bfMaleNavyRadio.addEventListener('change', toggleHipInput);
    bfFemaleNavyRadio.addEventListener('change', toggleHipInput);

    // --- Calculation Functions ---
    const calculateUSNavyBodyFat = () => {
        bfNavyError.textContent = '';
        const gender = bfMaleNavyRadio.checked ? 'male' : 'female';
        const heightCm = parseFloat(bfHeightNavyInput.value);
        const neckCm = parseFloat(bfNeckNavyInput.value);
        const waistCm = parseFloat(bfWaistNavyInput.value);
        const hipCm = gender === 'female' ? parseFloat(bfHipNavyInput.value) : 0;

        if (isNaN(heightCm) || isNaN(neckCm) || isNaN(waistCm) || heightCm <= 0 || neckCm <= 0 || waistCm <= 0) {
            bfNavyError.textContent = "الرجاء إدخال قيم صحيحة للطول والرقبة والخصر.";
            return null;
        }
        if (gender === 'female' && (isNaN(hipCm) || hipCm <= 0)) {
            bfNavyError.textContent = "الرجاء إدخال قيمة صحيحة للورك (للإناث).";
            return null;
        }

        let bodyFatPercentage;
        // Formulas use natural logarithm (log in JS is ln)
        // Height in inches for original formula: heightCm / 2.54
        if (gender === 'male') {
            // BFP = 86.010 * log10(waist - neck) - 70.041 * log10(height) + 36.76  (using log10)
            // BFP = 495 / (1.0324 - 0.19077 * log10(waistCm - neckCm) + 0.15456 * log10(heightCm)) - 450 (Hodgdon and Beckett, more common)
            // Using a common variation for cm:
            bodyFatPercentage = 86.010 * Math.log10(waistCm - neckCm) - 70.041 * Math.log10(heightCm) + 30.30; // Simplified constant based on some sources, might need +36.76 for men if original formula is strict
            // Let's use the more cited Hodgdon and Beckett formula adaptation for cm
            // BFP% = 495 / ( C1 - C2 * log10(waist-neck) + C3 * log10(height) ) - 450
            // For men: C1=1.0324, C2=0.19077, C3=0.15456
             bodyFatPercentage = 495 / (1.0324 - 0.19077 * Math.log10(waistCm - neckCm) + 0.15456 * Math.log10(heightCm)) - 450;

        } else { // female
            // BFP = 163.205 * log10(waist + hip - neck) - 97.684 * log10(height) - 78.387 (using log10)
            // Using Hodgdon and Beckett for cm
            // For women: C1=1.29579, C2=0.35004, C3=0.22100
            bodyFatPercentage = 495 / (1.29579 - 0.35004 * Math.log10(waistCm + hipCm - neckCm) + 0.22100 * Math.log10(heightCm)) - 450;
        }

        if (isNaN(bodyFatPercentage) || bodyFatPercentage < 0 || bodyFatPercentage > 70) { // Basic sanity check
             bfNavyError.textContent = "القيم المدخلة قد تكون غير واقعية أو خارج النطاق المسموح به للمعادلة.";
             return null;
        }
        return bodyFatPercentage;
    };

    const calculateBMIFormulaBodyFat = () => {
        bfBmiError.textContent = '';
        const gender = bfMaleBmiRadio.checked ? 'male' : 'female';
        const heightCm = parseFloat(bfHeightBmiInput.value);
        const weightKg = parseFloat(bfWeightBmiInput.value);
        const age = parseInt(bfAgeBmiInput.value, 10);

        if (isNaN(heightCm) || isNaN(weightKg) || isNaN(age) || heightCm <= 0 || weightKg <= 0 || age <= 0) {
            bfBmiError.textContent = "الرجاء إدخال قيم صحيحة للطول والوزن والعمر.";
            return null;
        }

        const heightM = heightCm / 100;
        const bmi = weightKg / (heightM * heightM);
        let bodyFatPercentage;

        if (gender === 'male') { // For adult males
            bodyFatPercentage = (1.20 * bmi) + (0.23 * age) - 16.2;
        } else { // For adult females
            bodyFatPercentage = (1.20 * bmi) + (0.23 * age) - 5.4;
        }
        // For children, a different formula is used (by Deurenberg et al.):
        // BFP% = (1.51 × BMI) – (0.70 × Age) – (3.6 × gender_code) + 1.4 (gender_code: male=1, female=0 for children)
        // Let's stick to adult formula for now for simplicity unless age < 18 then adjust
        if (age < 18) { // Example for children/teens
            const genderCode = (gender === 'male') ? 1 : 0;
            bodyFatPercentage = (1.51 * bmi) - (0.70 * age) - (3.6 * genderCode) + 1.4;
        }


        if (isNaN(bodyFatPercentage) || bodyFatPercentage < 0 || bodyFatPercentage > 70) { // Basic sanity check
             bfBmiError.textContent = "القيم المدخلة قد تكون غير واقعية.";
             return null;
        }
        return bodyFatPercentage;
    };

    // --- Result Display & Classification ---
    const getBodyFatCategory = (bfp, gender) => {
        let category = "غير محدد";
        let className = "";
        if (gender === 'male') {
            if (bfp <= 13) { category = "رياضي"; className = "athlete"; }
            else if (bfp <= 17) { category = "لائق"; className = "fitness"; }
            else if (bfp <= 24) { category = "متوسط/مقبول"; className = "average"; }
            else { category = "سمنة"; className = "obese"; }
        } else { // female
            if (bfp <= 20) { category = "رياضية"; className = "athlete"; }
            else if (bfp <= 24) { category = "لائقة"; className = "fitness"; }
            else if (bfp <= 31) { category = "متوسط/مقبول"; className = "average"; }
            else { category = "سمنة"; className = "obese"; }
        }
        return { text: category, className: className };
    };

    const displayResults = (bfp, weightKgForMassCalc) => {
        if (bfp === null || isNaN(bfp)) {
            resultDisplayArea.classList.add('hidden');
            infoTipsArea.classList.add('hidden');
            return;
        }

        const bodyFatPercentage = parseFloat(bfp.toFixed(1));
        bfPercentageResultSpan.textContent = `${bodyFatPercentage} %`;

        let fatMass = 0;
        let leanMass = 0;
        if (weightKgForMassCalc && weightKgForMassCalc > 0) { // weightKgForMassCalc is needed for mass
            fatMass = parseFloat(((bodyFatPercentage / 100) * weightKgForMassCalc).toFixed(1));
            leanMass = parseFloat((weightKgForMassCalc - fatMass).toFixed(1));
            fatMassResultSpan.textContent = `${fatMass} كجم`;
            leanMassResultSpan.textContent = `${leanMass} كجم`;
            fatMassResultSpan.parentElement.classList.remove('hidden');
            leanMassResultSpan.parentElement.classList.remove('hidden');
        } else {
            // Hide mass if weight not available for calculation (e.g. US Navy doesn't take weight directly)
            fatMassResultSpan.parentElement.classList.add('hidden');
            leanMassResultSpan.parentElement.classList.add('hidden');
        }


        const genderForCategory = bfMaleNavyRadio.checked || bfMaleBmiRadio.checked ? 'male' : 'female'; // Determine gender from active tab
        const category = getBodyFatCategory(bodyFatPercentage, genderForCategory);
        bfCategoryResultSpan.textContent = category.text;
        bfCategoryResultSpan.className = `category-badge ${category.className}`; // Reset then add new

        resultDisplayArea.classList.remove('hidden');
        infoTipsArea.classList.remove('hidden');

        // Optional: Scroll to results
        resultDisplayArea.scrollIntoView({ behavior: 'smooth', block: 'start' });
    };


    // --- Event Listeners for Calculation Buttons ---
    calculateNavyBtn.addEventListener('click', () => {
        const bfp = calculateUSNavyBodyFat();
        // For US Navy, we don't have weight as a direct input to calculate mass accurately here.
        // We could ask for it, or use a previously stored one. For now, display BFP and category.
        // If weight is needed, we'd get it from bfWeightBmiInput.value IF it's filled, or from profile.
        let weightForMassCalc = parseFloat(bfWeightBmiInput.value); // Try to get weight from BMI tab for convenience
        if(isNaN(weightForMassCalc) || weightForMassCalc <=0) weightForMassCalc = null; // If not valid, don't use

        displayResults(bfp, weightForMassCalc);
    });

    calculateBmiFormulaBtn.addEventListener('click', () => {
        const bfp = calculateBMIFormulaBodyFat();
        const weightKg = parseFloat(bfWeightBmiInput.value); // Weight is an input for this method
        displayResults(bfp, weightKg);
    });

    // --- Initial Setup ---
    toggleHipInput(); // Set initial visibility of hip input for US Navy
    bfMaleNavyRadio.checked = true; // Default gender for Navy
    bfMaleBmiRadio.checked = true;  // Default gender for BMI
    switchMethodTab('us-navy'); // Default to US Navy method

    calculateNavyBtn.hasBfListener = true; // Mark as initialized
    console.log("Body Fat Calculator Module Initialized Successfully.");
}

// --- Set the global initializer ---
window.currentPageInitializer = initializeBodyFatPageModule;