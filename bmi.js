// bmi.js

function initializeBmiPageModule() {
    console.log("Attempting to initialize BMI Page Module...");
    const app = window.app;
    if (!app) { console.error("Main app object (window.app) not found!"); return; }

    const calculateBtn = document.getElementById('calculate-bmi-btn');
    const weightInput = document.getElementById('weight');
    const heightInput = document.getElementById('height');
    const resultArea = document.getElementById('bmi-result-area');
    const tipsArea = document.getElementById('bmi-tips');
    const historyBody = document.getElementById('bmi-history-body');

    if (!calculateBtn || !historyBody) { console.error("BMI page core elements not found."); return; }
    if (calculateBtn.hasBmiListener) { console.log("BMI listeners already exist. Reloading history."); loadHistory(); return; }

    const formatDateTime = (isoString) => { /* ... (Same) ... */
        const date = new Date(isoString);
        return date.toLocaleString('ar-EG', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', hour12: true });
    };
    const getBmiTipsContent = (bmiClass) => { /* ... (Same) ... */
        let title = "<h4><i class='fas fa-lightbulb'></i> نصائح لك:</h4><ul>"; let tips = "";
        switch (bmiClass) {
            case 'underweight': tips = `<li>مؤشرك يدل على النحافة.</li><li>استشر طبيبًا.</li><li>ركز على تناول وجبات غنية.</li>`; break;
            case 'normal': tips = `<li>مؤشرك ضمن المعدل الطبيعي.</li><li>حافظ عليه.</li><li>استمر في تناول طعام متوازن ورياضة.</li>`; break;
            case 'overweight': tips = `<li>مؤشرك يدل على زيادة الوزن.</li><li>قلل السعرات وزد النشاط.</li><li>اختر الأطعمة الكاملة.</li>`; break;
            case 'obese': tips = `<li>مؤشرك يدل على السمنة.</li><li>ننصح باستشارة طبيب.</li><li>نظام صحي ورياضة.</li>`; break;
        }
        return title + tips + "</ul>";
    };
    const displayBmiResult = (bmi, category, bmiClass, range) => { /* ... (Same) ... */
        resultArea.innerHTML = `<div class="bmi-value ${bmiClass}">${bmi}</div><div class="bmi-category ${bmiClass}">${category}</div><div class="bmi-range-info">${range}</div>`;
        resultArea.className = `bmi-result-area ${bmiClass}`; tipsArea.innerHTML = getBmiTipsContent(bmiClass); tipsArea.classList.add('show');
    };

    const handleDeleteClick = async (id, rowElement) => {
        if (!confirm("هل أنت متأكد من حذف هذا السجل؟")) return;
        try {
            console.log(`Attempting to delete record with ID: ${id}`);
            await app.deleteData(app.bmiStoreName, id);
            console.log(`Record ${id} delete process finished in DB.`);
            rowElement.remove();
            app.showToast("تم حذف السجل بنجاح.");
            if (historyBody.rows.length === 0) {
                historyBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">لا يوجد سجل حتى الآن.</td></tr>';
            }
        } catch (error) {
            console.error("Failed to delete BMI record:", error);
            alert("فشل حذف السجل.");
        }
    };

    const addBmiRowToTable = (record) => {
        const row = document.createElement('tr');
        row.setAttribute('data-id', record.id);
        row.innerHTML = `<td>${formatDateTime(record.date)}</td><td>${record.weight}</td><td>${record.height}</td><td>${record.bmi}</td><td>${record.category}</td><td><button class="delete-row-btn" title="حذف"><i class="fas fa-trash-alt"></i></button></td>`;
        row.querySelector('.delete-row-btn').addEventListener('click', () => handleDeleteClick(Number(record.id), row));
        const placeholderRow = historyBody.querySelector('td[colspan="6"]');
        if (placeholderRow) placeholderRow.parentElement.remove();
        historyBody.prepend(row);
    };

    const loadHistory = async () => { /* ... (Same as before) ... */
        historyBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">يتم تحميل السجل...</td></tr>';
        try {
            const records = await app.getAllData(app.bmiStoreName);
            historyBody.innerHTML = '';
            if (records && records.length > 0) {
                records.sort((a, b) => new Date(b.date) - new Date(a.date));
                records.forEach(addBmiRowToTable);
            } else {
                historyBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">لا يوجد سجل حتى الآن.</td></tr>';
            }
        } catch (error) {
            console.error("Failed to load BMI history:", error);
            historyBody.innerHTML = '<tr><td colspan="6" style="text-align:center; color:red;">فشل تحميل السجل.</td></tr>';
        }
    };

    const handleCalculateClick = async () => { /* ... (Same as before) ... */
        const weight = parseFloat(weightInput.value); const height = parseFloat(heightInput.value);
        if (isNaN(weight) || isNaN(height) || weight <= 0 || height <= 0) {
            resultArea.innerHTML = '<p class="initial-message" style="color: #f44336;">الرجاء إدخال قيم صحيحة.</p>';
            resultArea.className = 'bmi-result-area'; tipsArea.classList.remove('show'); return;
        }
        const heightInMeters = height / 100; const bmi = (weight / (heightInMeters * heightInMeters)).toFixed(1);
        let category = '', bmiClass = '', range = '';
        if (bmi < 18.5) { category = 'نحافة'; bmiClass = 'underweight'; range = 'أقل من 18.5'; }
        else if (bmi >= 18.5 && bmi <= 24.9) { category = 'وزن طبيعي'; bmiClass = 'normal'; range = '18.5 - 24.9'; }
        else if (bmi >= 25 && bmi <= 29.9) { category = 'زيادة الوزن'; bmiClass = 'overweight'; range = '25 - 29.9'; }
        else { category = 'سمنة'; bmiClass = 'obese'; range = '30 أو أكثر'; }
        displayBmiResult(bmi, category, bmiClass, range);
        const newRecord = { weight, height, bmi, category, date: new Date().toISOString() };
        try {
            const newId = await app.saveData(app.bmiStoreName, newRecord);
            newRecord.id = newId;
            addBmiRowToTable(newRecord);
            app.showToast("تم الحفظ في السجل بنجاح!");
        } catch (error) {
            console.error("Failed to auto-save to history:", error);
            alert("حدث خطأ أثناء الحفظ في السجل.");
        }
    };

    calculateBtn.addEventListener('click', handleCalculateClick);
    calculateBtn.hasBmiListener = true;
    loadHistory();
    console.log("BMI Page Module Initialized Successfully.");
}

window.currentPageInitializer = initializeBmiPageModule;