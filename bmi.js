// This script runs WHEN the BMI page is loaded.
// It assumes `window.app` (with DB functions & toast) exists.

const BmiPage = {

    // Cache DOM elements for this page
    elements: {},

    // Store a reference to the global app object
    app: window.app,

    // Initialize the page: Find elements and attach listeners
    init: function() {
        console.log("Initializing BMI Page...");
        this.elements.calculateBtn = document.getElementById('calculate-bmi-btn');
        this.elements.weightInput = document.getElementById('weight');
        this.elements.heightInput = document.getElementById('height');
        this.elements.resultArea = document.getElementById('bmi-result-area');
        this.elements.tipsArea = document.getElementById('bmi-tips');
        this.elements.historyBody = document.getElementById('bmi-history-body');

        // Check if elements exist before adding listeners
        if (this.elements.calculateBtn) {
            this.elements.calculateBtn.addEventListener('click', this.handleCalculateClick.bind(this));
        } else {
            console.error("BMI Calculate button not found!");
            return; // Stop init if core elements missing
        }

        // Load existing history
        this.loadHistory();
        console.log("BMI Page Initialized.");
    },

    // Format Date (Helper)
    formatDateTime: function(isoString) {
        const date = new Date(isoString);
        return date.toLocaleString('ar-EG', {
            day: '2-digit', month: '2-digit', year: 'numeric',
            hour: '2-digit', minute: '2-digit', hour12: true
        });
    },

    // Get Tips (Helper)
    getBmiTipsContent: function(bmiClass) {
        let title = "<h4><i class='fas fa-lightbulb'></i> نصائح لك:</h4><ul>";
        let tips = "";
        switch (bmiClass) {
            case 'underweight': tips = `<li>مؤشرك يدل على النحافة. ننصح بزيادة الوزن بطريقة صحية.</li><li>استشر طبيبًا أو أخصائي تغذية.</li><li>ركز على تناول وجبات غنية.</li>`; break;
            case 'normal': tips = `<li>مؤشرك ضمن المعدل الطبيعي. تهانينا!</li><li>حافظ على هذا الإنجاز.</li><li>استمر في تناول طعام متوازن وممارسة الرياضة.</li>`; break;
            case 'overweight': tips = `<li>مؤشرك يدل على زيادة في الوزن.</li><li>حاول تقليل السعرات الحرارية وزيادة النشاط.</li><li>اختر الأطعمة الكاملة.</li>`; break;
            case 'obese': tips = `<li>مؤشرك يدل على السمنة.</li><li>ننصح بشدة باستشارة طبيب.</li><li>اتباع نظام غذائي صحي وممارسة الرياضة أمر حاسم.</li>`; break;
        }
        return title + tips + "</ul>";
    },

    // Display Result on UI
    displayBmiResult: function(bmi, category, bmiClass, range) {
        this.elements.resultArea.innerHTML = `
            <div class="bmi-value ${bmiClass}">${bmi}</div>
            <div class="bmi-category ${bmiClass}">${category}</div>
            <div class="bmi-range-info">${range}</div>
        `;
        this.elements.resultArea.className = `bmi-result-area ${bmiClass}`;
        this.elements.tipsArea.innerHTML = this.getBmiTipsContent(bmiClass);
        this.elements.tipsArea.classList.add('show');
    },

    // Add Row to Table UI
    addBmiRowToTable: function(record) {
        const row = document.createElement('tr');
        row.setAttribute('data-id', record.id);
        row.innerHTML = `
            <td>${this.formatDateTime(record.date)}</td>
            <td>${record.weight}</td>
            <td>${record.height}</td>
            <td>${record.bmi}</td>
            <td>${record.category}</td>
            <td><button class="delete-row-btn" title="حذف هذا السجل"><i class="fas fa-trash-alt"></i></button></td>
        `;
        row.querySelector('.delete-row-btn').addEventListener('click', (e) => {
            const rowToDelete = e.target.closest('tr');
            const idToDelete = Number(rowToDelete.getAttribute('data-id'));
            this.handleDeleteClick(idToDelete, rowToDelete);
        });
        const placeholderRow = this.elements.historyBody.querySelector('td[colspan="6"]');
        if (placeholderRow) placeholderRow.parentElement.remove();
        this.elements.historyBody.prepend(row);
    },

    // Handle Delete Button Click
    handleDeleteClick: async function(id, rowElement) {
        if (!confirm("هل أنت متأكد من حذف هذا السجل؟")) return;
        try {
            await this.app.deleteData(this.app.bmiStoreName, id);
            rowElement.remove();
            this.app.showToast("تم حذف السجل بنجاح.");
            if (this.elements.historyBody.rows.length === 0) {
                this.elements.historyBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">لا يوجد سجل حتى الآن.</td></tr>';
            }
        } catch (error) {
            console.error("Failed to delete BMI record:", error);
            alert("فشل حذف السجل.");
        }
    },

    // Load All History from DB
    loadHistory: async function() {
        this.elements.historyBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">يتم تحميل السجل...</td></tr>';
        try {
            const records = await this.app.getAllData(this.app.bmiStoreName);
            this.elements.historyBody.innerHTML = '';
            if (records && records.length > 0) {
                records.sort((a, b) => new Date(b.date) - new Date(a.date));
                records.forEach(this.addBmiRowToTable.bind(this)); // Use bind
            } else {
                this.elements.historyBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">لا يوجد سجل حتى الآن.</td></tr>';
            }
        } catch (error) {
            console.error("Failed to load BMI history:", error);
            this.elements.historyBody.innerHTML = '<tr><td colspan="6" style="text-align:center; color:red;">فشل تحميل السجل.</td></tr>';
        }
    },

    // Handle Calculate Button Click
    handleCalculateClick: async function() {
        const weight = parseFloat(this.elements.weightInput.value);
        const height = parseFloat(this.elements.heightInput.value);

        if (isNaN(weight) || isNaN(height) || weight <= 0 || height <= 0) {
            this.elements.resultArea.innerHTML = '<p class="initial-message" style="color: #f44336;">الرجاء إدخال قيم صحيحة.</p>';
            this.elements.resultArea.className = 'bmi-result-area';
            this.elements.tipsArea.classList.remove('show');
            return;
        }

        const heightInMeters = height / 100;
        const bmi = (weight / (heightInMeters * heightInMeters)).toFixed(1);
        let category = '', bmiClass = '', range = '';

        if (bmi < 18.5) { category = 'نحافة'; bmiClass = 'underweight'; range = 'أقل من 18.5'; }
        else if (bmi >= 18.5 && bmi <= 24.9) { category = 'وزن طبيعي'; bmiClass = 'normal'; range = '18.5 - 24.9'; }
        else if (bmi >= 25 && bmi <= 29.9) { category = 'زيادة الوزن'; bmiClass = 'overweight'; range = '25 - 29.9'; }
        else { category = 'سمنة'; bmiClass = 'obese'; range = '30 أو أكثر'; }

        this.displayBmiResult(bmi, category, bmiClass, range);

        const newRecord = { weight, height, bmi, category, date: new Date().toISOString() };

        try {
            const newId = await this.app.saveData(this.app.bmiStoreName, newRecord);
            newRecord.id = newId;
            this.addBmiRowToTable(newRecord);
            this.app.showToast("تم الحفظ في السجل بنجاح!");
        } catch (error) {
            console.error("Failed to auto-save to history:", error);
            alert("حدث خطأ أثناء الحفظ في السجل.");
        }
    }
};

// IMPORTANT: This line makes the BmiPage object accessible
// and defines its initialization method for the main script.
window.currentPageInitializer = BmiPage.init.bind(BmiPage);