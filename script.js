document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const themeToggle = document.getElementById('theme-toggle');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('overlay');
    const navLinks = document.querySelectorAll('.nav-link');
    const settingsToggle = document.getElementById('settings-toggle');
    const settingsCard = document.getElementById('settings-card');
    const closeSettings = document.getElementById('close-settings');
    const deleteDataButton = document.getElementById('delete-data');
    const body = document.body;
    const mainContent = document.getElementById('main-content');
    const pageCssLink = document.getElementById('page-specific-css');
    const toastMessage = document.getElementById('toast-message');
    const toastText = document.getElementById('toast-text');

    let db;
    let dbReady = false;
    const dbName = "SehtakDB";
    const dbVersion = 1;
    const bmiStoreName = 'bmiRecords'; // Make store name accessible globally
    const objectStores = [
        { name: bmiStoreName, keyPath: 'id', autoIncrement: true, indexes: [{ name: 'date', keyPath: 'date' }] },
        { name: 'userProfile', keyPath: 'setting', autoIncrement: false, indexes: [] },
    ];

    // --- Toast Message Function (Global) ---
    const showToast = (message) => {
        toastText.textContent = message;
        toastMessage.className = "toast show";
        setTimeout(() => { toastMessage.className = toastMessage.className.replace("show", "hide"); }, 3000);
        setTimeout(() => { toastMessage.className = "toast"; }, 3500);
    };

    // --- IndexedDB Setup & Functions (Global) ---
    const openDb = () => {
        return new Promise((resolve, reject) => {
            console.log("Attempting to open database...");
            const request = indexedDB.open(dbName, dbVersion);
            request.onerror = (event) => { console.error("Database error:", event.target.errorCode); dbReady = false; reject(event.target.errorCode); };
            request.onupgradeneeded = (event) => {
                db = event.target.result;
                objectStores.forEach(storeInfo => {
                    if (!db.objectStoreNames.contains(storeInfo.name)) {
                        const objectStore = db.createObjectStore(storeInfo.name, { keyPath: storeInfo.keyPath, autoIncrement: storeInfo.autoIncrement });
                        storeInfo.indexes.forEach(indexInfo => objectStore.createIndex(indexInfo.name, indexInfo.keyPath, { unique: false }));
                    }
                });
            };
            request.onsuccess = (event) => {
                db = event.target.result;
                dbReady = true;
                db.onversionchange = () => { db.close(); dbReady = false; alert("DB needs update. Please reload."); };
                db.onclose = () => { console.error("Database connection closed."); dbReady = false; db = null; };
                db.onerror = (event) => { console.error("DB connection error:", event.target.error); dbReady = false; };
                console.log("Database opened successfully.");
                resolve(db); // Resolve when DB is ready
            };
            request.onblocked = () => { console.warn("DB blocked."); reject("DB blocked"); };
        });
    };

    const checkDbReady = () => {
        if (!db || !dbReady) { alert("قاعدة البيانات غير متصلة."); return false; }
        try { db.objectStoreNames; } catch(e) { dbReady = false; db = null; alert("فقد الاتصال بقاعدة البيانات."); return false; }
        return true;
    };

    const saveData = (storeName, dataObject) => { /* ... (Same as before) ... */
        if (!checkDbReady()) return Promise.reject("Database not ready");
        return new Promise((resolve, reject) => {
            try {
                const transaction = db.transaction([storeName], 'readwrite');
                const request = transaction.objectStore(storeName).add(dataObject);
                transaction.oncomplete = () => resolve(request.result);
                transaction.onerror = (event) => reject(transaction.error);
            } catch (error) { reject(error); }
        });
    };
    const getAllData = (storeName) => { /* ... (Same as before) ... */
        if (!checkDbReady()) return Promise.reject("Database not ready");
        return new Promise((resolve, reject) => {
            try {
                const transaction = db.transaction([storeName], 'readonly');
                const request = transaction.objectStore(storeName).getAll();
                transaction.oncomplete = () => resolve(request.result);
                transaction.onerror = (event) => reject(transaction.error);
            } catch(error) { reject(error); }
        });
    };
    const deleteData = (storeName, key) => { /* ... (Same as before) ... */
        if (!checkDbReady()) return Promise.reject("Database not ready");
        return new Promise((resolve, reject) => {
             try {
                const transaction = db.transaction([storeName], 'readwrite');
                transaction.objectStore(storeName).delete(key);
                transaction.oncomplete = () => resolve();
                transaction.onerror = (event) => reject(transaction.error);
             } catch(error) { reject(error); }
        });
    };
    const deleteAllData = () => { /* ... (Same as before) ... */
        if (!checkDbReady()) return;
        const storeNames = Array.from(db.objectStoreNames);
        if (storeNames.length === 0) { alert("لا توجد بيانات لحذفها."); return; }
        if (!confirm("هل أنت متأكد؟")) return;
        try {
            const transaction = db.transaction(storeNames, 'readwrite');
            storeNames.forEach(storeName => transaction.objectStore(storeName).clear());
            transaction.oncomplete = () => {
                alert("تم حذف جميع البيانات!");
                settingsCard.classList.remove('show');
                overlay.classList.remove('show');
                // We need to reload the current page's history if applicable
                const activeLink = document.querySelector('.nav-link.active');
                if (activeLink && activeLink.dataset.page === 'bmi' && window.currentPageInitializer) {
                    // Re-init or specifically reload history
                    window.currentPageInitializer();
                }
            };
            transaction.onerror = (event) => alert("حدث خطأ أثناء الحذف.");
        } catch (error) { alert("فشل بدء الحذف."); }
    };
    deleteDataButton.addEventListener('click', deleteAllData);

    // --- Expose Global Functions/Variables ---
    window.app = {
        saveData,
        getAllData,
        deleteData,
        showToast,
        bmiStoreName // Expose store name
    };

    // --- Page Loading Logic ---
    const loadPage = async (htmlPath, cssPath, jsPath) => {
        mainContent.innerHTML = '<div class="loading-spinner"></div>'; // Show spinner

        // Remove old JS (if any - simple approach: remove script tag by id)
        const oldScript = document.getElementById('page-specific-js');
        if (oldScript) oldScript.remove();
        window.currentPageInitializer = null; // Reset initializer

        // Load CSS
        pageCssLink.href = cssPath || ''; // Set or clear CSS link

        try {
            // Load HTML
            const response = await fetch(htmlPath);
            if (!response.ok) throw new Error(`Could not load ${htmlPath}`);
            mainContent.innerHTML = await response.text();

            // Load JS (if exists)
            if (jsPath) {
                const script = document.createElement('script');
                script.id = 'page-specific-js';
                script.src = jsPath;
                script.onload = () => {
                    console.log(`${jsPath} loaded.`);
                    // Call the initializer function defined by the page script
                    if (window.currentPageInitializer && typeof window.currentPageInitializer === 'function') {
                        window.currentPageInitializer();
                    } else {
                         console.warn(`No initializer found for ${jsPath}`);
                    }
                };
                script.onerror = () => console.error(`Failed to load ${jsPath}`);
                document.body.appendChild(script);
            }

        } catch (error) {
            console.error("Error loading page:", error);
            mainContent.innerHTML = `<div class="page-content" style="color:red; text-align:center;"><h2>خطأ</h2><p>لم نتمكن من تحميل محتوى هذه الصفحة.</p></div>`;
        }
    };

    // --- Navigation ---
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const { html, css, js, page } = link.dataset;

            loadPage(html, css, js);

            navLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');
            window.location.hash = link.getAttribute('href'); // Update hash for history/bookmarking
            if (sidebar.classList.contains('show')) toggleSidebar();
        });
    });

    // --- Theme, Sidebar, Settings (Mostly Unchanged) ---
    const applyTheme = (theme) => { /* ... */ if (theme === 'dark') { body.setAttribute('data-theme', 'dark'); themeToggle.querySelector('i').className = 'fas fa-sun'; } else { body.removeAttribute('data-theme'); themeToggle.querySelector('i').className = 'fas fa-moon'; } };
    const toggleTheme = () => { /* ... */ let current = body.getAttribute('data-theme'); let newTheme = (current === 'dark' ? 'light' : 'dark'); applyTheme(newTheme); localStorage.setItem('theme', newTheme); };
    applyTheme(localStorage.getItem('theme'));
    themeToggle.addEventListener('click', toggleTheme);
    const toggleSidebar = () => { /* ... */ sidebar.classList.toggle('show'); overlay.classList.toggle('show'); };
    sidebarToggle.addEventListener('click', toggleSidebar);
    overlay.addEventListener('click', () => { /* ... */ if (sidebar.classList.contains('show')) toggleSidebar(); if (settingsCard.classList.contains('show')) { settingsCard.classList.remove('show'); overlay.classList.remove('show'); } });
    settingsToggle.addEventListener('click', () => { /* ... */ settingsCard.classList.add('show'); overlay.classList.add('show'); sidebar.classList.remove('show'); });
    closeSettings.addEventListener('click', () => { /* ... */ settingsCard.classList.remove('show'); overlay.classList.remove('show'); });

    // --- Initial Load ---
    const loadInitialPage = () => {
        const hash = window.location.hash || '#home';
        const initialLink = document.querySelector(`.nav-link[href="${hash}"]`) || document.querySelector('.nav-link[data-page="home"]');
        if (initialLink) {
            initialLink.click(); // Simulate click to load the page
        } else {
            loadPage('home.html', '', ''); // Fallback
        }
    };

    // Open DB first, then load the initial page
    openDb().then(() => {
        loadInitialPage();
    }).catch(error => {
        console.error("Failed to initialize DB. App might not work correctly.", error);
        mainContent.innerHTML = `<div class="page-content" style="color:red; text-align:center;"><h2>خطأ فادح</h2><p>لم نتمكن من فتح قاعدة البيانات. لا يمكن للتطبيق العمل.</p></div>`;
    });

}); // End of DOMContentLoaded