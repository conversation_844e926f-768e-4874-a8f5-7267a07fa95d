document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements (Same as before)
    const themeToggle = document.getElementById('theme-toggle');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('overlay');
    const navLinks = document.querySelectorAll('.nav-link');
    const settingsToggle = document.getElementById('settings-toggle');
    const settingsCard = document.getElementById('settings-card');
    const closeSettings = document.getElementById('close-settings');
    const deleteDataButton = document.getElementById('delete-data');
    const body = document.body;
    const mainContent = document.getElementById('main-content');
    const pageCssLink = document.getElementById('page-specific-css');
    const toastMessage = document.getElementById('toast-message');
    const toastText = document.getElementById('toast-text');

    let db;
    let dbReady = false;
    const dbName = "SehtakDB";
    const dbVersion = 2; // <-- ** Increment DB Version **
    const bmiStoreName = 'bmiRecords';
    const calorieStoreName = 'calorieRecords'; // <-- ** New Store Name **
    const objectStores = [
        { name: bmiStoreName, keyPath: 'id', autoIncrement: true, indexes: [{ name: 'date', keyPath: 'date' }] },
        { name: 'userProfile', keyPath: 'setting', autoIncrement: false, indexes: [] },
        // ** New Store Definition **
        { name: calorieStoreName, keyPath: 'id', autoIncrement: true, indexes: [{ name: 'date', keyPath: 'date' }] }
    ];

    // --- Toast Message Function (Same as before) ---
    const showToast = (message) => { /* ... (Same) ... */
        toastText.textContent = message;
        toastMessage.className = "toast show";
        setTimeout(() => { toastMessage.className = toastMessage.className.replace("show", "hide"); }, 3000);
        setTimeout(() => { toastMessage.className = "toast"; }, 3500);
    };

    // --- IndexedDB Setup & Functions (onupgradeneeded will now run) ---
    const openDb = () => {
        return new Promise((resolve, reject) => {
            console.log(`Attempting to open database '${dbName}' version ${dbVersion}...`);
            const request = indexedDB.open(dbName, dbVersion);
            request.onerror = (event) => { console.error("Database error:", event.target.errorCode); dbReady = false; reject(event.target.errorCode); };
            request.onupgradeneeded = (event) => {
                db = event.target.result;
                console.log(`Upgrading database to version ${dbVersion}...`);
                objectStores.forEach(storeInfo => {
                    if (!db.objectStoreNames.contains(storeInfo.name)) {
                        console.log(`Creating object store: ${storeInfo.name}`);
                        const objectStore = db.createObjectStore(storeInfo.name, { keyPath: storeInfo.keyPath, autoIncrement: storeInfo.autoIncrement });
                        storeInfo.indexes.forEach(indexInfo => objectStore.createIndex(indexInfo.name, indexInfo.keyPath, { unique: false }));
                    }
                    // You could add logic here to *modify* existing stores if needed in future versions.
                });
            };
            request.onsuccess = (event) => {
                db = event.target.result;
                dbReady = true;
                db.onversionchange = () => { db.close(); dbReady = false; alert("DB needs update. Please reload."); };
                db.onclose = () => { console.error("Database connection closed."); dbReady = false; db = null; };
                db.onerror = (event) => { console.error("DB connection error:", event.target.error); dbReady = false; };
                console.log("Database opened successfully.");
                resolve(db);
            };
            request.onblocked = () => { console.warn("DB blocked."); reject("DB blocked"); };
        });
    };
    const checkDbReady = () => { /* ... (Same) ... */
        if (!db || !dbReady) { console.error("DB not ready."); alert("قاعدة البيانات غير متصلة."); return false; }
        try { db.objectStoreNames; } catch(e) { dbReady = false; db = null; alert("فقد الاتصال."); return false; }
        return true;
    };
    const saveData = (storeName, dataObject) => { /* ... (Same) ... */
        if (!checkDbReady()) return Promise.reject("Database not ready");
        return new Promise((resolve, reject) => {
            try {
                const transaction = db.transaction([storeName], 'readwrite');
                const request = transaction.objectStore(storeName).add(dataObject);
                transaction.oncomplete = () => { console.log("Save TX complete. Key:", request.result); resolve(request.result); };
                transaction.onerror = (event) => { console.error("Save TX error:", transaction.error); reject(transaction.error); };
            } catch (error) { reject(error); }
        });
    };
    const getAllData = (storeName) => { /* ... (Same) ... */
        if (!checkDbReady()) return Promise.reject("Database not ready");
        return new Promise((resolve, reject) => {
            try {
                const transaction = db.transaction([storeName], 'readonly');
                const request = transaction.objectStore(storeName).getAll();
                transaction.oncomplete = () => { console.log("Get All TX complete."); resolve(request.result); };
                transaction.onerror = (event) => { console.error("Get All TX error:", transaction.error); reject(transaction.error); };
            } catch(error) { reject(error); }
        });
    };
    const deleteData = (storeName, key) => { /* ... (Same) ... */
        if (!checkDbReady()) return Promise.reject("Database not ready");
        return new Promise((resolve, reject) => {
             try {
                const transaction = db.transaction([storeName], 'readwrite');
                transaction.objectStore(storeName).delete(key);
                transaction.oncomplete = () => { console.log("Delete TX complete. Key:", key); resolve(); };
                transaction.onerror = (event) => { console.error("Delete TX error:", transaction.error); reject(transaction.error); };
             } catch(error) { reject(error); }
        });
    };
    const deleteAllData = () => { /* ... (Same, but now deletes from calorieRecords too) ... */
        if (!checkDbReady()) return;
        const storeNames = Array.from(db.objectStoreNames);
        if (storeNames.length === 0) { alert("لا توجد بيانات لحذفها."); return; }
        if (!confirm("هل أنت متأكد؟")) return;
        try {
            const transaction = db.transaction(storeNames, 'readwrite');
            storeNames.forEach(storeName => transaction.objectStore(storeName).clear());
            transaction.oncomplete = () => {
                console.log("Delete All transaction completed successfully.");
                alert("تم حذف جميع بياناتك بنجاح!");
                settingsCard.classList.remove('show');
                overlay.classList.remove('show');
                // Clear both tables if they exist
                const bmiHistoryBody = document.getElementById('bmi-history-body');
                if (bmiHistoryBody) bmiHistoryBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">لا يوجد سجل حتى الآن.</td></tr>';
                const calorieHistoryBody = document.getElementById('calorie-history-body');
                if (calorieHistoryBody) calorieHistoryBody.innerHTML = '<tr><td colspan="5" style="text-align:center;">لا يوجد سجل حتى الآن.</td></tr>';
            };
            transaction.onerror = (event) => { console.error("Delete All TX error:", transaction.error); alert("حدث خطأ أثناء الحذف."); };
        } catch (error) { console.error("Failed to start delete all TX:", error); alert("فشل بدء الحذف."); }
    };
    deleteDataButton.addEventListener('click', deleteAllData);

    // --- Expose Global Functions/Variables (Add new store name) ---
    window.app = { saveData, getAllData, deleteData, showToast, bmiStoreName, calorieStoreName }; // <-- Added calorieStoreName

    // --- Page Loading Logic (Same as before) ---
    const loadPage = async (htmlPath, cssPath, jsPath) => { /* ... (Same) ... */
        mainContent.innerHTML = '<div class="loading-spinner"></div>';
        const oldScript = document.getElementById('page-specific-js');
        if (oldScript) { console.log("Removing old script tag..."); oldScript.remove(); }
        window.currentPageInitializer = null;
        pageCssLink.href = cssPath || '';
        try {
            const response = await fetch(htmlPath);
            if (!response.ok) throw new Error(`Could not load ${htmlPath}`);
            mainContent.innerHTML = await response.text();
            if (jsPath) {
                const script = document.createElement('script');
                script.id = 'page-specific-js';
                script.src = jsPath;
                script.onload = () => {
                    console.log(`${jsPath} loaded.`);
                    if (window.currentPageInitializer && typeof window.currentPageInitializer === 'function') {
                        window.currentPageInitializer();
                    } else { console.warn(`No initializer found for ${jsPath}.`); }
                };
                script.onerror = () => console.error(`Failed to load ${jsPath}`);
                document.body.appendChild(script);
            }
        } catch (error) {
            console.error("Error loading page:", error);
            mainContent.innerHTML = `<div class="page-content" style="color:red; text-align:center;"><h2>خطأ</h2><p>لم نتمكن من تحميل الصفحة.</p></div>`;
        }
    };

    // --- Navigation & Others (Same as before) ---
    navLinks.forEach(link => { link.addEventListener('click', (e) => { e.preventDefault(); const { html, css, js } = link.dataset; loadPage(html, css, js); navLinks.forEach(l => l.classList.remove('active')); link.classList.add('active'); window.location.hash = link.getAttribute('href'); if (sidebar.classList.contains('show')) toggleSidebar(); }); });
    const applyTheme = (theme) => { if (theme === 'dark') { body.setAttribute('data-theme', 'dark'); themeToggle.querySelector('i').className = 'fas fa-sun'; } else { body.removeAttribute('data-theme'); themeToggle.querySelector('i').className = 'fas fa-moon'; } };
    const toggleTheme = () => { let current = body.getAttribute('data-theme'); let newTheme = (current === 'dark' ? 'light' : 'dark'); applyTheme(newTheme); localStorage.setItem('theme', newTheme); };
    applyTheme(localStorage.getItem('theme'));
    themeToggle.addEventListener('click', toggleTheme);
    const toggleSidebar = () => { sidebar.classList.toggle('show'); overlay.classList.toggle('show'); };
    sidebarToggle.addEventListener('click', toggleSidebar);
    overlay.addEventListener('click', () => { if (sidebar.classList.contains('show')) toggleSidebar(); if (settingsCard.classList.contains('show')) { settingsCard.classList.remove('show'); overlay.classList.remove('show'); } });
    settingsToggle.addEventListener('click', () => { settingsCard.classList.add('show'); overlay.classList.add('show'); sidebar.classList.remove('show'); });
    closeSettings.addEventListener('click', () => { settingsCard.classList.remove('show'); overlay.classList.remove('show'); });
    const loadInitialPage = () => { const hash = window.location.hash || '#home'; const initialLink = document.querySelector(`.nav-link[href="${hash}"]`) || document.querySelector('.nav-link[data-page="home"]'); if (initialLink) { initialLink.click(); } else { loadPage('home.html', '', ''); } };
    openDb().then(loadInitialPage).catch(error => { console.error("Failed to initialize DB.", error); mainContent.innerHTML = `<div class="page-content" style="color:red; text-align:center;"><h2>خطأ فادح</h2><p>لم نتمكن من فتح قاعدة البيانات.</p></div>`; });
});