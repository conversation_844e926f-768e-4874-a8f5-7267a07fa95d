document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const themeToggle = document.getElementById('theme-toggle');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('overlay');
    const navLinks = document.querySelectorAll('.nav-link');
    const settingsToggle = document.getElementById('settings-toggle');
    const settingsCard = document.getElementById('settings-card');
    const closeSettings = document.getElementById('close-settings');
    const deleteDataButton = document.getElementById('delete-data');
    const body = document.body;
    const mainContent = document.getElementById('main-content');
    const pageCssLink = document.getElementById('page-specific-css');
    const toastMessage = document.getElementById('toast-message');
    const toastText = document.getElementById('toast-text');

    let db;
    let dbReady = false;
    const dbName = "SehtakDB";
    const dbVersion = 6; // Version for bodyFatLog store
    const bmiStoreName = 'bmiRecords';
    const calorieStoreName = 'calorieRecords';
    const mealsLogStoreName = 'mealsLog';
    const userProfileStoreName = 'userProfile';
    const waterLogStoreName = 'waterLog';
    const activityLogStoreName = 'activityLog';
    const bodyFatLogStoreName = 'bodyFatLog'; // New store

    const objectStores = [
        { name: bmiStoreName, keyPath: 'id', autoIncrement: true, indexes: [{ name: 'date', keyPath: 'date' }] },
        { name: userProfileStoreName, keyPath: 'setting', autoIncrement: false, indexes: [] },
        { name: calorieStoreName, keyPath: 'id', autoIncrement: true, indexes: [{ name: 'date', keyPath: 'date' }] },
        { name: mealsLogStoreName, keyPath: 'id', autoIncrement: true, indexes: [{ name: 'date', keyPath: 'date' }, { name: 'mealType', keyPath: 'mealType' }] },
        { name: waterLogStoreName, keyPath: 'id', autoIncrement: true, indexes: [{ name: 'date', keyPath: 'date' }, { name: 'timestamp', keyPath: 'timestamp' }] },
        { name: activityLogStoreName, keyPath: 'id', autoIncrement: true, indexes: [{ name: 'date', keyPath: 'date' }, { name: 'activityName', keyPath: 'activityName' }] },
        { name: bodyFatLogStoreName, keyPath: 'id', autoIncrement: true, indexes: [{ name: 'date', keyPath: 'date' }, { name: 'method', keyPath: 'method' }] }
    ];

    const showToast = (message) => {
        if (!toastText || !toastMessage) {
            console.warn("Toast elements not found for message:", message);
            return;
        }
        toastText.textContent = message;
        toastMessage.className = "toast show";
        setTimeout(() => { toastMessage.className = toastMessage.className.replace("show", "hide"); }, 3000);
        setTimeout(() => { toastMessage.className = "toast"; }, 3500);
    };

    const openDb = () => {
        return new Promise((resolve, reject) => {
            console.log(`Attempting to open database '${dbName}' version ${dbVersion}...`);
            const request = indexedDB.open(dbName, dbVersion);

            request.onerror = (event) => {
                console.error("Database error:", event.target.errorCode || event.target.error);
                dbReady = false;
                reject(event.target.errorCode || event.target.error);
            };

            request.onupgradeneeded = (event) => {
                db = event.target.result;
                console.log(`Upgrading database from version ${event.oldVersion} to ${dbVersion}...`);
                objectStores.forEach(storeInfo => {
                    if (!db.objectStoreNames.contains(storeInfo.name)) {
                        console.log(`Creating object store: ${storeInfo.name}`);
                        const objectStore = db.createObjectStore(storeInfo.name, { keyPath: storeInfo.keyPath, autoIncrement: storeInfo.autoIncrement });
                        storeInfo.indexes.forEach(indexInfo => {
                            if (!objectStore.indexNames.contains(indexInfo.name)) {
                                objectStore.createIndex(indexInfo.name, indexInfo.keyPath, { unique: false });
                                console.log(`Created index '${indexInfo.name}' on store '${storeInfo.name}'`);
                            }
                        });
                    } else {
                        console.log(`Object store '${storeInfo.name}' already exists.`);
                        const transaction = event.target.transaction;
                        if (transaction) {
                            const existingStore = transaction.objectStore(storeInfo.name);
                            storeInfo.indexes.forEach(indexInfo => {
                               if (!existingStore.indexNames.contains(indexInfo.name)) {
                                    existingStore.createIndex(indexInfo.name, indexInfo.keyPath, { unique: false });
                                    console.log(`Dynamically added index '${indexInfo.name}' to existing store '${storeInfo.name}'.`);
                               }
                            });
                        }
                    }
                });
            };

            request.onsuccess = (event) => {
                db = event.target.result;
                dbReady = true;
                db.onversionchange = () => {
                    if (db) db.close();
                    dbReady = false;
                    db = null;
                    alert("تم تحديث قاعدة البيانات في تبويب آخر. يرجى إعادة تحميل هذه الصفحة.");
                    console.warn("DB version change requested, connection closed.");
                };
                db.onclose = () => {
                    console.error("Database connection closed unexpectedly.");
                    dbReady = false;
                    db = null;
                };
                db.onerror = (event) => {
                    console.error("Database connection error (via db.onerror):", event.target.error);
                    dbReady = false;
                };
                console.log("Database opened successfully.");
                resolve(db);
            };

            request.onblocked = () => {
                console.warn("Database open request blocked. Please close other tabs/windows using this application.");
                reject("DB blocked");
                alert("لا يمكن فتح قاعدة البيانات لأنها قيد الاستخدام في تبويب آخر. يرجى إغلاق التبويبات الأخرى وإعادة المحاولة.");
            };
        });
    };

    const checkDbReady = () => {
        if (!db || !dbReady) {
            console.error("DB not ready or connection closed.");
            // alert("قاعدة البيانات غير متصلة أو مغلقة. يرجى إعادة تحميل الصفحة."); // Removed to avoid redundant alerts
            return false;
        }
        try {
            return db.objectStoreNames.length >= 0; // A simple check
        } catch (e) {
            console.error("DB connection check failed (likely closed):", e);
            dbReady = false;
            db = null;
            // alert("فقد الاتصال بقاعدة البيانات. يرجى إعادة تحميل الصفحة."); // Removed
            return false;
        }
    };

    const saveData = (storeName, dataObject) => { // Uses .add() for new records
        if (!checkDbReady()) return Promise.reject(`Database not ready for saving to ${storeName}`);
        return new Promise((resolve, reject) => {
            try {
                const transaction = db.transaction([storeName], 'readwrite');
                const objectStore = transaction.objectStore(storeName);
                const request = objectStore.add(dataObject);
                transaction.oncomplete = () => resolve(request.result);
                transaction.onerror = () => { console.error(`Save TX error for ${storeName}:`, transaction.error); reject(transaction.error); };
            } catch (error) { console.error(`Catch during save to ${storeName}:`, error); reject(error); }
        });
    };

    const putData = (storeName, dataObject) => { // Uses .put() for adding or updating records
        if (!checkDbReady()) return Promise.reject(`Database not ready for putData to ${storeName}`);
        return new Promise((resolve, reject) => {
            try {
                const transaction = db.transaction([storeName], 'readwrite');
                const objectStore = transaction.objectStore(storeName);
                const request = objectStore.put(dataObject);
                transaction.oncomplete = () => resolve(request.result);
                transaction.onerror = () => { console.error(`PutData TX error for ${storeName}:`, transaction.error); reject(transaction.error); };
            } catch (error) { console.error(`Catch during putData to ${storeName}:`, error); reject(error); }
        });
    };

    const putUserProfileSetting = (settingObject) => {
        return putData(userProfileStoreName, settingObject);
    };

    const getUserProfileSetting = (settingKey) => {
        if (!checkDbReady()) return Promise.reject(`Database not ready for getUserProfileSetting for key ${settingKey}`);
        return new Promise((resolve, reject) => {
            try {
                const transaction = db.transaction([userProfileStoreName], 'readonly');
                const store = transaction.objectStore(userProfileStoreName);
                const request = store.get(settingKey);
                transaction.oncomplete = () => resolve(request.result);
                transaction.onerror = () => { console.error(`Get UserProfile TX error for key ${settingKey}:`, transaction.error); reject(transaction.error); };
            } catch (error) { console.error(`Catch during Get UserProfile for key ${settingKey}:`, error); reject(error); }
        });
    };

    const getAllData = (storeName) => {
        if (!checkDbReady()) return Promise.reject(`Database not ready for getAllData from ${storeName}`);
        return new Promise((resolve, reject) => {
            try {
                const transaction = db.transaction([storeName], 'readonly');
                const objectStore = transaction.objectStore(storeName);
                const request = objectStore.getAll();
                transaction.oncomplete = () => resolve(request.result);
                transaction.onerror = () => { console.error(`Get All TX error for ${storeName}:`, transaction.error); reject(transaction.error); };
            } catch(error) { console.error(`Catch during Get All for ${storeName}:`, error); reject(error); }
        });
    };

    const getDataByIndex = (storeName, indexName, key) => {
        if (!checkDbReady()) return Promise.reject(`Database not ready for getDataByIndex from ${storeName}/${indexName}`);
        if (typeof storeName === 'undefined') { console.error("getDataByIndex called with undefined storeName."); return Promise.reject("Store name is undefined for getDataByIndex."); }
        return new Promise((resolve, reject) => {
            try {
                const transaction = db.transaction([storeName], 'readonly');
                const store = transaction.objectStore(storeName);
                const index = store.index(indexName);
                const request = index.getAll(key);
                transaction.oncomplete = () => resolve(request.result);
                transaction.onerror = () => { console.error(`Get By Index TX error for ${storeName}/${indexName} with key ${key}:`, transaction.error); reject(transaction.error); };
            } catch (error) { console.error(`Catch during Get By Index for ${storeName}/${indexName} with key ${key}:`, error); reject(error); }
        });
    };

    const deleteData = (storeName, key) => {
        if (!checkDbReady()) return Promise.reject(`Database not ready for deleteData from ${storeName}`);
        return new Promise((resolve, reject) => {
            try {
                const transaction = db.transaction([storeName], 'readwrite');
                transaction.objectStore(storeName).delete(key);
                transaction.oncomplete = () => resolve();
                transaction.onerror = () => { console.error(`Delete TX error for ${storeName}, key ${key}:`, transaction.error); reject(transaction.error); };
            } catch(error) { console.error(`Catch during Delete for ${storeName}, key ${key}:`, error); reject(error); }
        });
    };

    const deleteAllData = () => {
        if (!checkDbReady()) { alert("قاعدة البيانات غير جاهزة للحذف."); return; }
        const storeNames = Array.from(db.objectStoreNames);
        if (storeNames.length === 0) { alert("لا توجد بيانات لحذفها."); return; }
        if (!confirm("هل أنت متأكد من رغبتك في حذف جميع البيانات؟")) return;
        try {
            const transaction = db.transaction(storeNames, 'readwrite');
            storeNames.forEach(storeName => {
                if (db.objectStoreNames.contains(storeName)) {
                    transaction.objectStore(storeName).clear();
                }
            });
            transaction.oncomplete = () => {
                alert("تم حذف جميع بياناتك بنجاح!");
                if(settingsCard) settingsCard.classList.remove('show');
                if(overlay) overlay.classList.remove('show');
                const uiClearTargets = ['bmi-history-body', 'calorie-history-body', 'meals-log-table-body', 'water-history-body', 'activity-history-body', 'bodyfat-history-body'];
                uiClearTargets.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.innerHTML = `<tr><td colspan="100%" style="text-align:center;">لا يوجد سجل حتى الآن.</td></tr>`;
                });
                const activeLink = document.querySelector('.nav-link.active');
                if (activeLink && window.currentPageInitializer && typeof window.currentPageInitializer === 'function') {
                    console.log("Re-initializing current page UI after deleting all data.");
                    window.currentPageInitializer();
                }
            };
            transaction.onerror = (event) => { console.error("Delete All TX error:", transaction.error); alert("حدث خطأ أثناء الحذف."); };
        } catch (error) { console.error("Catch during Delete All transaction start:", error); alert("فشل بدء عملية الحذف."); }
    };
    if(deleteDataButton) deleteDataButton.addEventListener('click', deleteAllData);

    window.app = {
        saveData, putData, getAllData, deleteData, putUserProfileSetting, getUserProfileSetting, getDataByIndex,
        showToast, bmiStoreName, calorieStoreName, mealsLogStoreName, userProfileStoreName, waterLogStoreName,
        activityLogStoreName, bodyFatLogStoreName
    };

    const loadPage = async (htmlPath, cssPath, jsPath) => {
        if(mainContent) mainContent.innerHTML = '<div class="loading-spinner"></div>';
        const oldScript = document.getElementById('page-specific-js');
        if (oldScript) { oldScript.remove(); }
        window.currentPageInitializer = null;
        if(pageCssLink) pageCssLink.href = cssPath || '';

        try {
            const response = await fetch(htmlPath);
            if (!response.ok) throw new Error(`Could not load ${htmlPath}. Status: ${response.status}`);
            if(mainContent) mainContent.innerHTML = await response.text();

            if (jsPath) {
                const script = document.createElement('script');
                script.id = 'page-specific-js';
                script.src = jsPath;
                script.onload = () => {
                    console.log(`${jsPath} loaded successfully.`);
                    if (window.currentPageInitializer && typeof window.currentPageInitializer === 'function') {
                        console.log(`Calling initializer for ${jsPath}`);
                        window.currentPageInitializer();
                    } else {
                        console.warn(`No currentPageInitializer function found or set for ${jsPath}. Ensure it's defined in ${jsPath} and assigned to window.currentPageInitializer.`);
                    }
                };
                script.onerror = () => console.error(`Failed to load script: ${jsPath}`);
                document.body.appendChild(script);
            }
        } catch (error) {
            console.error("Error loading page content:", error);
            if(mainContent) mainContent.innerHTML = `<div class="page-content error-display"><h2>خطأ في تحميل الصفحة</h2><p>${error.message}</p></div>`;
        }
    };

    const setupNavigation = () => {
        if (navLinks && navLinks.length > 0) {
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const { html, css, js } = link.dataset;
                    if (!html) {
                        console.error("Link is missing data-html attribute:", link);
                        return;
                    }
                    loadPage(html, css, js);
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                    if (window.location.hash !== link.getAttribute('href')) {
                        window.location.hash = link.getAttribute('href');
                    }
                    if (sidebar && sidebar.classList.contains('show')) toggleSidebar();
                });
            });
        } else {
            console.error("CRITICAL: No navigation links found with class '.nav-link'. Navigation will not function.");
        }
    };

    const loadInitialPage = () => {
        if (!navLinks || navLinks.length === 0) {
            console.warn("loadInitialPage: navLinks not available or empty. Attempting to load home.html directly.");
            loadPage('home.html', '', '');
            return;
        }
        const hash = window.location.hash || '#home';
        let initialLink = document.querySelector(`.nav-link[href="${hash}"]`);
        if (!initialLink) {
            initialLink = navLinks[0];
            console.warn(`Hash '${hash}' did not match any nav link. Defaulting to first link: ${initialLink ? initialLink.href : 'None'}`);
            if (initialLink) window.location.hash = initialLink.getAttribute('href');
        }

        if (initialLink) {
            const { html, css, js } = initialLink.dataset;
            loadPage(html, css, js);
            navLinks.forEach(l => l.classList.remove('active'));
            initialLink.classList.add('active');
        } else {
            console.error("CRITICAL: No initial link could be determined even after fallback. Defaulting to load home.html directly.");
            loadPage('home.html', '', '');
        }
    };

    function applyTheme(theme) {
        if (theme === 'dark') {
            body.setAttribute('data-theme', 'dark');
            if (themeToggle) themeToggle.querySelector('i').className = 'fas fa-sun';
        } else {
            body.removeAttribute('data-theme');
            if (themeToggle) themeToggle.querySelector('i').className = 'fas fa-moon';
        }
    }
    function toggleTheme() {
        let current = body.getAttribute('data-theme');
        let newTheme = (current === 'dark' ? 'light' : 'dark');
        applyTheme(newTheme);
        localStorage.setItem('theme', newTheme);
    }
    function toggleSidebar() {
        if(sidebar) sidebar.classList.toggle('show');
        if(overlay) overlay.classList.toggle('show');
    }

    if(themeToggle) themeToggle.addEventListener('click', toggleTheme);
    if(sidebarToggle) sidebarToggle.addEventListener('click', toggleSidebar);
    if(overlay) overlay.addEventListener('click', () => {
        if (sidebar && sidebar.classList.contains('show')) toggleSidebar();
        if (settingsCard && settingsCard.classList.contains('show')) {
            settingsCard.classList.remove('show');
            overlay.classList.remove('show');
        }
    });
    if(settingsToggle) settingsToggle.addEventListener('click', () => {
        if(settingsCard) settingsCard.classList.add('show');
        if(overlay) overlay.classList.add('show');
        if (sidebar && sidebar.classList.contains('show')) sidebar.classList.remove('show');
    });
    if(closeSettings) closeSettings.addEventListener('click', () => {
        if(settingsCard) settingsCard.classList.remove('show');
        if(overlay) overlay.classList.remove('show');
    });
    
    applyTheme(localStorage.getItem('theme'));

    // --- Initialize App ---
    openDb().then(() => {
        console.log("DB Opened successfully. Setting up navigation and loading initial page.");
        setupNavigation();
        loadInitialPage();
    }).catch(error => {
        console.error("CRITICAL: Failed to initialize DB. Application may not function correctly.", error);
        if(mainContent) mainContent.innerHTML = `<div class="page-content error-display"><h2>خطأ فادح في قاعدة البيانات</h2><p>لم نتمكن من فتح قاعدة البيانات.</p><p>${error.message || error}</p></div>`;
    });
});