// tracker.js

function initializeTrackerPageModule() {
    console.log("Initializing Meal Tracker Page Module (with Chart)...");
    const app = window.app;
    if (!app) { console.error("Main app object (window.app) not found!"); return; }

    // --- DOM Elements ---
    // ... (نفس عناصر DOM من الرد السابق)
    const dailyGoalsContent = document.getElementById('daily-goals-content');
    const setGoalsTip = document.getElementById('set-goals-tip');
    const mealDateInput = document.getElementById('meal-date');
    const mealTypeSelect = document.getElementById('meal-type');
    const foodNameInput = document.getElementById('food-name');
    const foodQuantityInput = document.getElementById('food-quantity');
    const mealCaloriesInput = document.getElementById('meal-calories');
    const mealProteinInput = document.getElementById('meal-protein');
    const mealCarbsInput = document.getElementById('meal-carbs');
    const mealFatInput = document.getElementById('meal-fat');
    const addMealBtn = document.getElementById('add-meal-btn');
    const updateMealBtn = document.getElementById('update-meal-btn');
    const cancelEditBtn = document.getElementById('cancel-edit-btn');
    const mealErrorMsg = document.getElementById('meal-error');
    const logDatePicker = document.getElementById('log-date-picker');
    const summaryDateSpan = document.getElementById('summary-date');
    const caloriesProgress = document.getElementById('calories-progress');
    const proteinProgress = document.getElementById('protein-progress');
    const carbsProgress = document.getElementById('carbs-progress');
    const fatProgress = document.getElementById('fat-progress');
    const caloriesSummaryText = document.getElementById('calories-summary-text');
    const proteinSummaryText = document.getElementById('protein-summary-text');
    const carbsSummaryText = document.getElementById('carbs-summary-text');
    const fatSummaryText = document.getElementById('fat-summary-text');
    const mealsListUl = document.getElementById('meals-list');
    const chartCanvas = document.getElementById('calorieProgressChart'); // ** New Canvas **
    const chartErrorMsg = document.getElementById('chart-error-message'); // ** New Chart Error Msg **


    let dailyTargets = null;
    let currentlyEditingMealId = null;
    let calorieProgressChartInstance = null; // ** To hold Chart.js instance **

    if (!addMealBtn || !logDatePicker || !mealsListUl || !chartCanvas) {
        console.error("Tracker page essential elements (including chart canvas) not found. Initialization failed.");
        return;
    }
    if (addMealBtn.hasTrackerListener) {
        console.log("Tracker listeners already exist.");
        loadDailyGoals().then(() => {
            loadLogForDate(logDatePicker.value || formatDate(new Date()));
            loadChartData(); // Load chart data too
        });
        return;
    }

    const formatDate = (dateObj) => { // Expects Date object or YYYY-MM-DD string
        const d = new Date(dateObj); // Handles both
        const month = '' + (d.getMonth() + 1);
        const day = '' + d.getDate();
        const year = d.getFullYear();
        return [year, month.padStart(2, '0'), day.padStart(2, '0')].join('-');
    };

    // --- Load Daily Goals (Same as before) ---
    const loadDailyGoals = async () => { /* ... (Same) ... */
        try {
            const targetsSetting = await app.getUserProfileSetting('dailyTargets');
            if (targetsSetting && targetsSetting.value) {
                dailyTargets = targetsSetting.value;
                dailyGoalsContent.innerHTML = `<div class="goal-item"><span>السعرات</span><strong>${dailyTargets.calories || '--'}</strong></div><div class="goal-item"><span>البروتين (جم)</span><strong>${dailyTargets.protein || '--'}</strong></div><div class="goal-item"><span>الكربوهيدرات (جم)</span><strong>${dailyTargets.carbs || '--'}</strong></div><div class="goal-item"><span>الدهون (جم)</span><strong>${dailyTargets.fat || '--'}</strong></div>`;
                setGoalsTip.classList.add('hidden'); return true;
            } else {
                dailyGoalsContent.innerHTML = `<p>لم يتم تعيين الأهداف بعد.</p>`; setGoalsTip.classList.remove('hidden');
                dailyTargets = { calories: 2000, protein: 100, carbs: 200, fat: 60 }; console.warn("Daily targets not set, using defaults."); return false;
            }
        } catch (error) { console.error("Error loading daily goals:", error); dailyGoalsContent.innerHTML = `<p style="color:red;">خطأ في تحميل الأهداف.</p>`; setGoalsTip.classList.remove('hidden'); dailyTargets = { calories: 2000, protein: 100, carbs: 200, fat: 60 }; return false; }
    };

    // --- Daily Log & Summary (Same as before) ---
    const updateDailySummary = (mealsForDay) => { /* ... (Same) ... */
        let consumed = { calories: 0, protein: 0, carbs: 0, fat: 0 };
        mealsForDay.forEach(meal => { consumed.calories += meal.calories || 0; consumed.protein += meal.protein || 0; consumed.carbs += meal.carbs || 0; consumed.fat += meal.fat || 0; });
        if (!dailyTargets) { loadDailyGoals().then(() => updateDailySummary(mealsForDay)); return; }
        caloriesSummaryText.textContent = `${consumed.calories} / ${dailyTargets.calories}`; proteinSummaryText.textContent = `${consumed.protein} / ${dailyTargets.protein} جم`; carbsSummaryText.textContent = `${consumed.carbs} / ${dailyTargets.carbs} جم`; fatSummaryText.textContent = `${consumed.fat} / ${dailyTargets.fat} جم`;
        const calcPercent = (consumed, goal) => goal > 0 ? Math.min((consumed / goal) * 100, 100) : 0;
        caloriesProgress.style.width = `${calcPercent(consumed.calories, dailyTargets.calories)}%`; proteinProgress.style.width = `${calcPercent(consumed.protein, dailyTargets.protein)}%`; carbsProgress.style.width = `${calcPercent(consumed.carbs, dailyTargets.carbs)}%`; fatProgress.style.width = `${calcPercent(consumed.fat, dailyTargets.fat)}%`;
    };
    const renderMealsForDay = (meals) => { /* ... (Same, including meal type badge fix) ... */
        mealsListUl.innerHTML = '';
        if (meals.length === 0) { mealsListUl.innerHTML = '<li class="no-meals">لم يتم تسجيل أي وجبات لهذا اليوم.</li>'; return; }
        meals.forEach(meal => {
            const li = document.createElement('li'); li.setAttribute('data-meal-id', meal.id);
            const mealTypeOption = Array.from(mealTypeSelect.options).find(opt => opt.value === meal.mealType);
            const mealTypeText = mealTypeOption ? mealTypeOption.textContent : meal.mealType;
            const mealTypeClass = meal.mealType.toLowerCase();
            li.innerHTML = `<div class="meal-item-header"><strong>${meal.foodName}</strong><span class="meal-type-badge ${mealTypeClass}">${mealTypeText}</span></div><div class="meal-item-details"><p>الكمية: <span>${meal.quantity || '--'}</span></p><div class="meal-item-macros"><div>السعرات: <strong>${meal.calories || 0}</strong></div><div>بروتين: <strong>${meal.protein || 0}ج</strong></div><div>كارب: <strong>${meal.carbs || 0}ج</strong></div><div>دهون: <strong>${meal.fat || 0}ج</strong></div></div></div><div class="meal-item-actions"><button class="edit-meal-btn" title="تعديل"><i class="fas fa-edit"></i> تعديل</button><button class="delete-meal-btn" title="حذف"><i class="fas fa-trash-alt"></i> حذف</button></div>`;
            li.querySelector('.delete-meal-btn').addEventListener('click', () => deleteMeal(meal.id, li));
            li.querySelector('.edit-meal-btn').addEventListener('click', () => populateFormForEdit(meal));
            mealsListUl.appendChild(li);
        });
    };
    const loadLogForDate = async (dateString) => { /* ... (Same) ... */
        summaryDateSpan.textContent = new Date(dateString + 'T00:00:00').toLocaleDateString('ar-EG', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
        try {
            const mealsForDay = await app.getDataByIndex(app.mealsLogStoreName, 'date', dateString);
            renderMealsForDay(mealsForDay); updateDailySummary(mealsForDay);
        } catch (error) { console.error(`Error loading log for date ${dateString}:`, error); mealsListUl.innerHTML = '<li class="no-meals" style="color:red;">خطأ في تحميل السجل.</li>';}
    };

    // --- Add/Edit/Delete Meal (Same as before) ---
    const resetMealForm = () => { /* ... (Same) ... */ foodNameInput.value = ''; foodQuantityInput.value = ''; mealCaloriesInput.value = ''; mealProteinInput.value = ''; mealCarbsInput.value = ''; mealFatInput.value = ''; mealTypeSelect.value = 'breakfast'; mealDateInput.value = formatDate(new Date()); mealErrorMsg.textContent = ''; addMealBtn.classList.remove('hidden'); updateMealBtn.classList.add('hidden'); cancelEditBtn.classList.add('hidden'); currentlyEditingMealId = null; };
    const populateFormForEdit = (meal) => { /* ... (Same) ... */ currentlyEditingMealId = meal.id; mealDateInput.value = meal.date; mealTypeSelect.value = meal.mealType; foodNameInput.value = meal.foodName; foodQuantityInput.value = meal.quantity; mealCaloriesInput.value = meal.calories; mealProteinInput.value = meal.protein; mealCarbsInput.value = meal.carbs; mealFatInput.value = meal.fat; addMealBtn.classList.add('hidden'); updateMealBtn.classList.remove('hidden'); cancelEditBtn.classList.remove('hidden'); foodNameInput.focus(); app.showToast("يمكنك تعديل بيانات الوجبة."); };
    cancelEditBtn.addEventListener('click', resetMealForm);
    const handleAddOrUpdateMeal = async (isUpdate = false) => { /* ... (Same, ensure app.putData logic or workaround exists) ... */
        mealErrorMsg.textContent = ''; const mealData = { date: mealDateInput.value, mealType: mealTypeSelect.value, foodName: foodNameInput.value.trim(), quantity: foodQuantityInput.value.trim(), calories: parseInt(mealCaloriesInput.value, 10) || 0, protein: parseInt(mealProteinInput.value, 10) || 0, carbs: parseInt(mealCarbsInput.value, 10) || 0, fat: parseInt(mealFatInput.value, 10) || 0, };
        if (!mealData.date || !mealData.foodName) { mealErrorMsg.textContent = 'الرجاء إدخال التاريخ واسم الطعام.'; return; }
        if (isUpdate && currentlyEditingMealId) mealData.id = currentlyEditingMealId;
        try {
            if (isUpdate) {
                if (app.putData) { await app.putData(app.mealsLogStoreName, mealData); } // Assuming putData is added to app similar to putUserProfileSetting
                else { if(currentlyEditingMealId) await app.deleteData(app.mealsLogStoreName, currentlyEditingMealId); delete mealData.id; await app.saveData(app.mealsLogStoreName, mealData); }
                app.showToast("تم تحديث الوجبة!");
            } else { await app.saveData(app.mealsLogStoreName, mealData); app.showToast("تمت إضافة الوجبة!"); }
            resetMealForm(); await loadLogForDate(logDatePicker.value); await loadChartData(); // ** Refresh chart **
        } catch (error) { console.error("Error saving meal:", error); mealErrorMsg.textContent = 'خطأ في حفظ الوجبة.'; }
    };
    addMealBtn.addEventListener('click', () => handleAddOrUpdateMeal(false));
    updateMealBtn.addEventListener('click', () => handleAddOrUpdateMeal(true));
    const deleteMeal = async (mealId, listItemElement) => { /* ... (Same) ... */
        if (!confirm("هل أنت متأكد؟")) return;
        try {
            await app.deleteData(app.mealsLogStoreName, mealId); listItemElement.remove(); app.showToast("تم حذف الوجبة.");
            await loadLogForDate(logDatePicker.value); await loadChartData(); // ** Refresh chart **
        } catch (error) { console.error("Error deleting meal:", error); alert("فشل حذف الوجبة."); }
    };

    // --- Chart Functions ---
    const renderProgressChart = (chartData) => {
        if (!chartCanvas) return;
        const ctx = chartCanvas.getContext('2d');
        if (calorieProgressChartInstance) {
            calorieProgressChartInstance.destroy(); // Destroy previous instance
        }

        const isDarkMode = document.body.getAttribute('data-theme') === 'dark';
        const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
        const labelColor = isDarkMode ? '#ccc' : '#333';

        calorieProgressChartInstance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.labels, // Array of date strings
                datasets: [{
                    label: 'السعرات الحرارية المستهلكة',
                    data: chartData.consumedData, // Array of calorie numbers
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1,
                    fill: true,
                }
                // Future: Add Goal Calories dataset
                // {
                // label: 'السعرات الحرارية الهدف',
                // data: chartData.goalData,
                // borderColor: 'rgb(255, 99, 132)',
                // tension: 0.1,
                // }
            ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'السعرات الحرارية', color: labelColor },
                        ticks: { color: labelColor },
                        grid: { color: gridColor }
                    },
                    x: {
                        title: { display: true, text: 'التاريخ', color: labelColor },
                        ticks: { color: labelColor },
                        grid: { color: gridColor }
                    }
                },
                plugins: {
                    legend: { labels: { color: labelColor } }
                }
            }
        });
    };

    const loadChartData = async () => {
        console.log("Loading chart data...");
        chartErrorMsg.classList.add('hidden');
        try {
            const today = new Date();
            const labels = [];
            const consumedData = [];
            // const goalData = []; // For future goal line

            if (!dailyTargets) { // Ensure goals are loaded for context if needed later
                await loadDailyGoals();
            }
            const currentGoalCalories = dailyTargets ? dailyTargets.calories : 2000; // Fallback

            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(today.getDate() - i);
                const dateString = formatDate(date);
                labels.push(date.toLocaleDateString('ar-EG', { day: '2-digit', month: 'short' }));

                const mealsForDay = await app.getDataByIndex(app.mealsLogStoreName, 'date', dateString);
                let totalCaloriesConsumed = 0;
                mealsForDay.forEach(meal => totalCaloriesConsumed += (meal.calories || 0));
                consumedData.push(totalCaloriesConsumed);
                // goalData.push(currentGoalCalories); // Example if showing goal line
            }

            if (consumedData.every(val => val === 0) && labels.length > 0) {
                 console.log("No calorie data for the last 7 days to display on chart.");
                 chartErrorMsg.textContent = "لا توجد بيانات سعرات لعرضها في الرسم البياني لآخر 7 أيام.";
                 chartErrorMsg.classList.remove('hidden');
                 if (calorieProgressChartInstance) calorieProgressChartInstance.destroy(); // Clear chart if no data
                 chartCanvas.style.display = 'none'; // Hide canvas
                 return;
            }
            chartCanvas.style.display = 'block'; // Show canvas

            renderProgressChart({ labels, consumedData /*, goalData */ });
        } catch (error) {
            console.error("Error loading chart data:", error);
            chartErrorMsg.textContent = "حدث خطأ أثناء تحميل بيانات الرسم البياني.";
            chartErrorMsg.classList.remove('hidden');
        }
    };

    // --- Event Listeners & Initial Load ---
    logDatePicker.addEventListener('change', (e) => {
        loadLogForDate(e.target.value);
        // Chart data doesn't change with daily log picker, it's always last 7 days from 'today'
    });

    // --- Initialization ---
    const initializePage = async () => {
        const todayStr = formatDate(new Date());
        mealDateInput.value = todayStr;
        logDatePicker.value = todayStr;
        await loadDailyGoals();
        await loadLogForDate(todayStr);
        await loadChartData(); // ** Load chart on page init **
        resetMealForm();
    };

    initializePage();
    addMealBtn.hasTrackerListener = true;
    console.log("Meal Tracker Page Module (with Chart) Initialized Successfully.");
}
// --- Set the global initializer ---
window.currentPageInitializer = initializeTrackerPageModule;