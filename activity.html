<div class="activity-tracker-container page-content">
    <h2><i class="fas fa-running"></i> متتبع النشاط البدني</h2>
    <p class="description">سجل تمارينك وأنشطتك وتتبع تقدمك نحو أهدافك.</p>

    <div class="activity-goals-card calculator-card">
        <h3><i class="fas fa-flag-checkered"></i> أهدافك الأسبوعية للنشاط</h3>
        <div class="input-row">
            <div class="input-group">
                <label for="weekly-minutes-goal">هدف الدقائق الأسبوعي:</label>
                <input type="number" id="weekly-minutes-goal" placeholder="مثال: 150 دقيقة">
            </div>
            <div class="input-group">
                <label for="weekly-calories-goal">هدف السعرات الأسبوعي (المحروقة):</label>
                <input type="number" id="weekly-calories-goal" placeholder="مثال: 1500 سعر">
            </div>
        </div>
        <button id="set-activity-goals-btn"><i class="fas fa-save"></i> حفظ الأهداف الأسبوعية</button>
        <div id="current-weekly-goals-display" style="text-align: center; margin-top: 15px;">
            </div>
    </div>


    <div class="tracker-layout">
        <div class="activity-input-card calculator-card">
            <h3 id="activity-form-title"><i class="fas fa-plus-circle"></i> تسجيل نشاط جديد</h3>
            <div class="input-group">
                <label for="activity-date">التاريخ:</label>
                <input type="date" id="activity-date">
            </div>
            <div class="input-group">
                <label for="current-weight-activity">وزنك الحالي (كجم) (لحساب أدق للسعرات):</label>
                <input type="number" id="current-weight-activity" placeholder="مثال: 70">
                <small class="subtle-text">سيتم استخدام هذا الوزن لحساب السعرات. يمكنك تحديثه هنا أو من حاسبة BMI/السعرات.</small>
            </div>
            <div class="input-group">
                <label for="activity-type-filter">ابحث عن نشاط أو اختر:</label>
                <input type="text" id="activity-type-filter" placeholder="اكتب للبحث..." style="margin-bottom: 5px;">
                <select id="activity-type" size="5" style="height: auto;">
                    <option value="">-- اختر نشاط --</option>
                    </select>
            </div>
            <div class="input-group">
                <label for="activity-duration">مدة النشاط (بالدقائق):</label>
                <input type="number" id="activity-duration" placeholder="مثال: 30">
            </div>

            <div class="input-group">
                <input type="checkbox" id="manual-calories-toggle" style="margin-left: 5px;">
                <label for="manual-calories-toggle" style="display: inline; font-weight: normal;">إدخال السعرات المحروقة يدويًا؟</label>
            </div>
            <div id="manual-calories-input-group" class="input-group hidden">
                <label for="manual-calories-burned">السعرات المحروقة يدويًا:</label>
                <input type="number" id="manual-calories-burned" placeholder="مثال: 300">
            </div>

            <div class="input-group">
                <label for="activity-intensity">الشدة (اختياري):</label>
                <select id="activity-intensity">
                    <option value="">-- اختر الشدة --</option>
                    <option value="منخفضة">منخفضة</option>
                    <option value="متوسطة">متوسطة</option>
                    <option value="عالية">عالية</option>
                    <option value="أخرى">أخرى (اذكر في الملاحظات)</option>
                </select>
            </div>
            <div class="input-group">
                <label for="activity-distance">المسافة (كم - اختياري):</label>
                <input type="number" id="activity-distance" placeholder="مثال: 5">
            </div>
            <div class="input-group">
                <label for="activity-notes">ملاحظات (اختياري):</label>
                <textarea id="activity-notes" rows="2" placeholder="مثال: شعور جيد بعد التمرين"></textarea>
            </div>
            <button id="log-activity-btn"><i class="fas fa-save"></i> تسجيل النشاط</button>
            <button id="update-activity-btn" class="secondary-button hidden"><i class="fas fa-edit"></i> تحديث النشاط</button>
            <button id="cancel-edit-activity-btn" class="danger-button hidden" style="margin-top:10px;"><i class="fas fa-times"></i> إلغاء</button>
            <p id="activity-error" class="error-message"></p>
        </div>

        <div class="activity-summary-history-card calculator-card">
            <h3><i class="fas fa-calendar-check"></i> ملخص وسجل الأنشطة</h3>
            <div class="input-group">
                <label for="log-activity-date-picker">عرض سجل تاريخ:</label>
                <input type="date" id="log-activity-date-picker">
            </div>

            <div class="daily-activity-summary">
                <h4>ملخص اليوم (<span id="activity-summary-date">--</span>):</h4>
                <p>إجمالي مدة النشاط: <strong id="total-activity-duration">0</strong> دقيقة</p>
                <p>إجمالي السعرات المحروقة: <strong id="total-calories-burned">0</strong> سعر حراري</p>
            </div>

            <div class="activity-history-list-container">
                <h4>الأنشطة المسجلة لليوم:</h4>
                <ul id="activity-history-list">
                    <li class="no-entries">لم يتم تسجيل أي أنشطة لهذا اليوم.</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="weekly-activity-summary-card calculator-card">
        <h3><i class="fas fa-calendar-alt"></i> ملخص النشاط الأسبوعي (آخر 7 أيام)</h3>
        <div id="weekly-activity-stats-content">
             <p>إجمالي الدقائق: <strong id="weekly-total-minutes">--</strong> / <span id="weekly-minutes-goal-display">--</span> دقيقة</p>
             <div class="progress-bar-container small-progress">
                <div class="progress-bar" id="weekly-minutes-progress" style="width: 0%;"></div>
             </div>
             <p>إجمالي السعرات المحروقة: <strong id="weekly-total-calories">--</strong> / <span id="weekly-calories-goal-display">--</span> سعر</p>
             <div class="progress-bar-container small-progress">
                <div class="progress-bar protein-bar" id="weekly-calories-progress" style="width: 0%;"></div>
             </div>
             <p>أعلى سعرات محروقة في يوم: <strong id="weekly-highest-calories">--</strong> سعر</p>
             <p>أقل سعرات محروقة في يوم (مع تسجيل): <strong id="weekly-lowest-calories">--</strong> سعر</p>
        </div>
        <div class="chart-container" style="position: relative; height:300px; width:100%; margin-top: 20px;">
            <canvas id="activityWeeklyChart"></canvas>
        </div>
        <p id="activity-chart-error" class="error-message hidden"></p>
    </div>
</div>