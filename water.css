/* water.css - Styles for Water Intake Tracker (Enhanced) */

.water-tracker-container { max-width: 1000px; margin: 0 auto; }
.water-tracker-container h2 { text-align: center; color: var(--primary-color); margin-bottom: 15px; font-size: 1.8rem; }
.water-tracker-container h2 i { margin-left: 10px; }
.water-tracker-container .description { text-align: center; color: #888; margin-bottom: 30px; font-size: 1.1rem; }

.calculator-card { background-color: var(--card-background); padding: 25px 30px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.05); border: 1px solid var(--border-color); margin-bottom: 30px; }
.calculator-card h3 { text-align: center; color: var(--primary-color); margin-top: 0; margin-bottom: 25px; font-size: 1.5rem; }
.calculator-card h3 i { margin-left: 8px; }

.water-goal-card { border-top: 5px solid var(--primary-color); }
.current-goal-display { text-align: center; font-size: 1.2rem; margin-bottom: 15px; }
.current-goal-display span { color: var(--text-color); }
.current-goal-display strong { color: var(--secondary-color); font-size: 1.5rem; margin-right: 5px; }
#set-water-goal-btn { width: auto; padding: 10px 20px; font-size: 1rem; display: block; margin: 15px auto 10px; background-color: var(--secondary-color); color: white; border:none; border-radius: 6px; cursor: pointer; }
#set-water-goal-btn:hover { background-color: var(--primary-color); }
#set-water-goal-btn i { margin-left: 8px; }
.subtle-text { font-size: 0.9rem; color: #777; text-align: center; }
.subtle-text.hidden { display: none; }
[data-theme="dark"] .subtle-text { color: #aaa; }
[data-theme="dark"] .subtle-text strong { color: var(--text-color); }

.water-layout { display: grid; grid-template-columns: 1fr 1.2fr; gap: 30px; align-items: flex-start; }
.input-group { margin-bottom: 15px; } /* Reduced margin */
.input-group label { display: block; margin-bottom: 8px; font-weight: 600; color: var(--text-color); }
.input-group input[type="number"],
.input-group input[type="date"],
.input-group input[type="time"], /* Added time */
.input-group textarea { /* Added textarea */
    width: 100%; padding: 12px 15px; border: 1px solid var(--border-color); border-radius: 8px;
    font-size: 1rem; background-color: var(--background-color); color: var(--text-color); font-family: 'Cairo', sans-serif;
    line-height: 1.5; /* For textarea */
}
.input-group textarea { resize: vertical; min-height: 60px; }
.input-group input:focus, .input-group textarea:focus { outline: none; border-color: var(--secondary-color); box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2); }
.quick-add-buttons { display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; margin-bottom: 15px; }
.quick-add-btn { padding: 10px; font-size: 0.9rem; background-color: var(--secondary-color); color: white; border: none; border-radius: 6px; cursor: pointer; transition: background-color 0.2s; }
.quick-add-btn:hover { background-color: var(--primary-color); }
#add-water-btn, #update-water-btn, #cancel-edit-water-btn { width: 100%; padding: 14px; border: none; border-radius: 8px; font-size: 1.1rem; cursor: pointer; transition: background-color 0.3s; font-weight: 600; display: flex; justify-content: center; align-items: center; background-color: var(--primary-color); color: #fff; margin-top: 10px; }
#add-water-btn i, #update-water-btn i, #cancel-edit-water-btn i { margin-left: 8px; }
#add-water-btn:hover { background-color: var(--secondary-color); }
#update-water-btn { background-color: var(--success-color) !important; }
#update-water-btn:hover { background-color: #388E3C !important; }
#cancel-edit-water-btn { background-color: #757575 !important; }
#cancel-edit-water-btn:hover { background-color: #616161 !important; }
.error-message { color: var(--delete-color); text-align: center; margin-top: 10px; font-weight: 600; min-height: 1em; }
.hidden { display: none !important; }

.water-summary-history-card #log-water-date-picker { margin-bottom: 25px; }
.daily-water-summary { margin-bottom: 30px; text-align: center; }
.daily-water-summary h4 { font-size: 1.2rem; color: var(--secondary-color); margin-bottom: 20px; }
.water-progress-visual { display: flex; flex-direction: column; align-items: center; gap: 15px; }
.water-glass { width: 100px; height: 150px; border: 3px solid var(--primary-color); border-top: 5px solid var(--primary-color); border-radius: 0 0 30px 30px; position: relative; background-color: rgba(255,255,255,0.1); overflow: hidden; margin: 0 auto; box-shadow: inset 0 -5px 10px rgba(0,0,0,0.1); }
[data-theme="dark"] .water-glass { border-color: var(--secondary-color); background-color: rgba(0,0,0,0.2); box-shadow: inset 0 -5px 10px rgba(0,0,0,0.3); }
.water-level { position: absolute; bottom: 0; left: 0; width: 100%; height: 0%; background: linear-gradient(to top, #64B5F6, #90CAF9); transition: height 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94), background-color 0.7s; border-radius: 0 0 27px 27px; display: flex; align-items: center; justify-content: center; }
.water-percentage { color: rgba(255,255,255,0.9); font-weight: bold; font-size: 1.5rem; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); opacity: 0; transition: opacity 0.5s ease-in-out; }
.water-level.show-percentage .water-percentage { opacity: 1; }
[data-theme="dark"] .water-level { background: linear-gradient(to top, var(--primary-color), var(--secondary-color));}
[data-theme="dark"] .water-percentage { color: rgba(255,255,255,0.9); }
.daily-water-summary .summary-text p { margin: 5px 0; font-size: 1.1rem; }
.daily-water-summary .summary-text strong { color: var(--primary-color); }

.water-history-list-container h4 { font-size: 1.2rem; color: var(--secondary-color); margin-bottom: 15px; border-top: 1px dashed var(--border-color); padding-top: 20px; margin-top: 20px; }
#water-history-list { list-style: none; padding: 0; max-height: 300px; overflow-y: auto; }
#water-history-list li {
    background-color: var(--card-background);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.04);
}
#water-history-list li.no-entries { text-align: center; color: #888; padding: 20px; background-color: transparent; border: none; box-shadow: none;}
.water-log-entry-main { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
.water-entry-info .water-entry-time { font-size: 0.85rem; color: #777; margin-right: 10px; }
[data-theme="dark"] .water-entry-info .water-entry-time { color: #aaa; }
.water-entry-info .water-entry-amount { font-weight: 700; font-size: 1.1rem; color: var(--primary-color); }
.water-entry-notes { font-size: 0.9rem; color: #555; margin-top: 8px; padding-top: 8px; border-top: 1px dashed var(--border-color); white-space: pre-wrap; /* Preserve line breaks */ }
[data-theme="dark"] .water-entry-notes { color: #ccc; border-top-color: var(--border-color); }
.water-log-actions button { background: none; border: none; cursor: pointer; padding: 5px; font-size: 0.95rem; }
.water-log-actions .edit-water-btn { color: var(--secondary-color); margin-left: 8px; }
.water-log-actions .delete-water-btn { color: var(--delete-color); }

.weekly-stats-card { border-top: 5px solid var(--secondary-color); }
#weekly-stats-content { text-align: right; }
#weekly-stats-content p { font-size: 1.1rem; margin-bottom: 10px; color: var(--text-color); }
#weekly-stats-content strong { font-weight: bold; color: var(--primary-color); margin-right: 5px; }
#weekly-stats-content .loading-stats { color: #888; }
.chart-container { padding: 10px; background-color: var(--background-color); border-radius: 8px; border: 1px solid var(--border-color); }

@media (max-width: 992px) { .water-layout { grid-template-columns: 1fr; } }
@media (max-width: 768px) { .calculator-card { padding: 20px; } .calculator-card h3 { font-size: 1.3rem; } .quick-add-buttons { grid-template-columns: repeat(2, 1fr); } .current-goal-display strong {font-size: 1.3rem;} .chart-container { height: 300px !important; } }
@media (max-width: 480px) { .quick-add-buttons { grid-template-columns: 1fr; } .water-glass { width: 80px; height: 120px; } .daily-water-summary .summary-text p { font-size: 1rem; } #weekly-stats-content p {font-size: 1rem;} .chart-container { height: 250px !important; } }