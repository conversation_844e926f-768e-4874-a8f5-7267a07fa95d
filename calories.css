/* Calories Page Specific Styles */
.calories-container { max-width: 700px; margin: 0 auto; }
.calories-container h2 { text-align: center; color: var(--primary-color); margin-bottom: 15px; font-size: 1.8rem; }
.calories-container h2 i { margin-left: 10px; }
.calories-container .description { text-align: center; color: #888; margin-bottom: 30px; font-size: 1.1rem; }
.calculator-card { background-color: var(--card-background); padding: 30px 40px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05); border: 1px solid var(--border-color); margin-bottom: 30px; }
.input-row { display: flex; justify-content: space-between; gap: 30px; margin-bottom: 25px; flex-wrap: wrap; } /* Added wrap */
.input-row .input-group { flex: 1; margin-bottom: 0; min-width: 250px; } /* Added min-width */
.input-group { margin-bottom: 25px; }
.input-group.full-width { width: 100%; }
.input-group label { display: block; margin-bottom: 10px; font-weight: 600; color: var(--text-color); font-size: 1.1rem; }
.input-group input[type="number"], .input-group select { width: 100%; padding: 15px 20px; border: 1px solid var(--border-color); border-radius: 8px; font-size: 1.1rem; background-color: var(--background-color); color: var(--text-color); transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out; text-align: right; font-family: 'Cairo', sans-serif; }
.input-group select { cursor: pointer; appearance: none; background-image: url('data:image/svg+xml;utf8,<svg fill="currentColor" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"></path></svg>'); background-repeat: no-repeat; background-position: left 15px center; padding-left: 45px; }
[data-theme="dark"] .input-group select { background-image: url('data:image/svg+xml;utf8,<svg fill="white" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"></path></svg>'); }
.input-group input:focus, .input-group select:focus { outline: none; border-color: var(--secondary-color); box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2); }

/* Gender & Units Options (Shared Style) */
.gender-options, .units-options { display: flex; border: 1px solid var(--border-color); border-radius: 8px; overflow: hidden; }
.gender-options input[type="radio"], .units-options input[type="radio"] { display: none; }
.gender-options label, .units-options label { flex: 1; padding: 15px; text-align: center; cursor: pointer; background-color: var(--background-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; font-size: 1.1rem; font-weight: 600; margin-bottom: 0; display: flex; align-items: center; justify-content: center; }
.gender-options label i, .units-options label i { margin-left: 8px; }
.gender-options input[type="radio"]:checked + label, .units-options input[type="radio"]:checked + label { background-color: var(--primary-color); color: #fff; border-color: var(--primary-color); }
.gender-options label:first-of-type, .units-options label:first-of-type { border-left: 1px solid var(--border-color); }

#calculate-calories-btn { width: 100%; padding: 16px; border: none; border-radius: 8px; font-size: 1.3rem; cursor: pointer; transition: background-color 0.3s, transform 0.1s; font-weight: 700; display: flex; justify-content: center; align-items: center; background-color: var(--primary-color); color: #fff; box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3); margin-top: 15px; }
#calculate-calories-btn:hover { background-color: var(--secondary-color); }
#calculate-calories-btn i { margin-left: 10px; }
.error-message { color: var(--delete-color); text-align: center; margin-top: 15px; font-weight: 600; min-height: 1.2em; }

/* Results & Macros Area */
.calories-result-area { background-color: var(--card-background); padding: 30px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05); border: 1px solid var(--border-color); margin-bottom: 30px; border-top: 5px solid var(--primary-color); }
.calories-result-area.hidden { display: none; }
.calories-result-area h3, .calories-result-area h4 { text-align: center; color: var(--primary-color); margin-bottom: 25px; margin-top: 10px; font-size: 1.5rem; }
.calories-result-area h4 { font-size: 1.3rem; color: var(--secondary-color); border-top: 1px dashed var(--border-color); padding-top: 25px; }
.calories-result-area h3 i, .calories-result-area h4 i { margin-left: 8px; }
.results-list { list-style: none; padding: 0; margin: 0; }
.results-list li { display: flex; justify-content: space-between; align-items: center; padding: 18px 10px; border-bottom: 1px solid var(--border-color); font-size: 1.15rem; }
.results-list li:last-child { border-bottom: none; }
.results-list li span { color: var(--text-color); }
.results-list li strong { color: var(--primary-color); font-weight: 700; margin-left: 10px; }
.disclaimer { text-align: center; font-size: 0.9rem; color: #888; margin-top: 20px; font-style: italic; }
.macros-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; text-align: center; }
.macro-item { background-color: var(--background-color); padding: 20px; border-radius: 10px; border: 1px solid var(--border-color); }
.macro-item h4 { margin-bottom: 10px; font-size: 1.1rem; color: var(--text-color); border: none; padding: 0; }
.macro-item strong { font-size: 1.8rem; color: var(--secondary-color); display: block; }
.carb-color { color: #FF9800 !important; } /* Orange */
.protein-color { color: #f44336 !important; } /* Red */
.fat-color { color: #9C27B0 !important; } /* Purple */

/* Tips Area */
.tips-area { background-color: #e3f2fd; border: 1px solid #bbdefb; padding: 25px; border-radius: 15px; margin-bottom: 30px; }
[data-theme="dark"] .tips-area { background-color: #1A237E; border: 1px solid #303F9F; }
.tips-area.hidden { display: none; }
.tips-area h3 { text-align: center; color: var(--primary-color); margin-bottom: 20px; font-size: 1.5rem; }
.tips-area ul { list-style: none; padding-right: 0; }
.tips-area ul li { background-color: var(--card-background); margin-bottom: 10px; padding: 15px; border-radius: 8px; display: flex; align-items: center; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
.tips-area ul li i { margin-left: 15px; color: var(--primary-color); font-size: 1.3rem; width: 25px; }

/* History Table */
.history-container { margin-top: 40px; background-color: var(--card-background); padding: 25px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1); border: 1px solid var(--border-color); }
.history-container h3 { text-align: center; color: var(--primary-color); margin-bottom: 20px; font-size: 1.5rem; }
.table-wrapper { overflow-x: auto; }
#calorie-history-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
#calorie-history-table th, #calorie-history-table td { border: 1px solid var(--border-color); padding: 12px 10px; text-align: center; vertical-align: middle; font-size: 0.95rem; }
#calorie-history-table th { background-color: var(--primary-color); color: #fff; }
#calorie-history-table tbody tr:nth-child(even) { background-color: var(--background-color); }
#calorie-history-table tbody tr:hover { background-color: var(--secondary-color); color: #fff; transition: background-color 0.2s ease; }
.delete-row-btn { background-color: var(--delete-color); color: #fff; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; font-size: 0.9rem; transition: background-color 0.2s ease; }
.delete-row-btn:hover { background-color: #c62828; }

/* ... (جميع التنسيقات السابقة للسعرات كما هي) ... */

/* Responsive adjustments for Calories Page */
@media (max-width: 768px) {
    .calories-container {
        max-width: 100%;
    }

    .calculator-card {
        padding: 25px;
    }

    .input-row {
        flex-direction: column; /* تكديس عناصر الصف عموديًا */
        gap: 20px; /* تقليل المسافة بين العناصر المكدسة */
    }

    .input-row .input-group {
        min-width: 100%; /* اجعل مجموعات الإدخال تأخذ العرض الكامل */
    }

    .macros-grid {
        grid-template-columns: 1fr; /* عمود واحد لشبكة المغذيات */
    }

    .calories-result-area {
        padding: 20px;
    }
    .results-list li {
        font-size: 1.05rem;
        padding: 15px 8px;
    }
    .results-list li strong {
        font-size: 1.1rem;
    }

    .macros-grid .macro-item strong {
        font-size: 1.5rem;
    }

    .tips-area {
        padding: 20px;
    }
    .tips-area ul li {
        padding: 12px;
        font-size: 0.9rem;
    }

    #calorie-history-table th,
    #calorie-history-table td {
        padding: 10px 8px; /* تقليل الحشو في خلايا الجدول */
        font-size: 0.9rem; /* تصغير خط الجدول */
    }
}

@media (max-width: 480px) {
    .calculator-card {
        padding: 20px;
    }
    .input-group label {
        font-size: 1rem;
    }
    .input-group input[type="number"],
    .input-group select {
        font-size: 1rem;
        padding: 12px 15px;
    }
     .gender-options label, .units-options label {
        font-size: 1rem;
        padding: 12px;
    }
    #calculate-calories-btn {
        font-size: 1.1rem;
        padding: 14px;
    }

    .calories-result-area h3, .calories-result-area h4 {
        font-size: 1.3rem;
        margin-bottom: 20px;
    }
     .calories-result-area h4 {
        font-size: 1.1rem;
    }
    .results-list li {
        font-size: 0.95rem;
    }
     .results-list li strong {
        font-size: 1rem;
    }
    .macro-item h4 {
        font-size: 1rem;
    }
     .macro-item strong {
        font-size: 1.3rem;
    }

    .tips-area h3 {
        font-size: 1.3rem;
    }
    .tips-area ul li {
        font-size: 0.85rem;
    }
     .tips-area ul li i {
        font-size: 1.1rem;
    }
}

/* في calories.css */
#set-daily-goals-btn {
    width: auto; /* أو 100% حسب التصميم */
    padding: 12px 25px;
    background-color: var(--secondary-color); /* أو لون آخر مناسب */
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background-color 0.3s;
    font-weight: 600;
    display: block; /* أو inline-block */
    margin: 20px auto 0; /* للتوسيط إذا كان block */
}
#set-daily-goals-btn:hover {
    background-color: #64b5f6; /* أفتح قليلاً */
}
#set-daily-goals-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}