// water.js

function initializeWaterPageModule() {
    console.log("Initializing Enhanced Water Tracker Page Module...");
    const app = window.app;
    if (!app) { console.error("Main app object (window.app) not found!"); return; }

    // --- DOM Elements ---
    const waterDateInput = document.getElementById('water-date');
    const waterTimeInput = document.getElementById('water-time'); // New
    const waterAmountInput = document.getElementById('water-amount');
    const waterNotesInput = document.getElementById('water-notes'); // New
    const quickAddButtons = document.querySelectorAll('.quick-add-btn');
    const addWaterBtn = document.getElementById('add-water-btn');
    const updateWaterBtn = document.getElementById('update-water-btn'); // New
    const cancelEditWaterBtn = document.getElementById('cancel-edit-water-btn'); // New
    const waterFormTitle = document.getElementById('water-form-title'); // New
    const waterErrorMsg = document.getElementById('water-error');

    const logDatePicker = document.getElementById('log-water-date-picker');
    const summaryDateSpan = document.getElementById('water-summary-date');
    const totalWaterTodaySpan = document.getElementById('total-water-today');
    const summaryDailyWaterGoalSpan = document.getElementById('summary-daily-water-goal');
    const waterLevelIndicator = document.getElementById('water-level-indicator');
    const waterPercentageText = document.getElementById('water-percentage-text');
    const waterHistoryListUl = document.getElementById('water-history-list');

    const currentGoalDisplayStrong = document.getElementById('current-daily-water-goal');
    const customGoalInput = document.getElementById('custom-water-goal');
    const setGoalBtn = document.getElementById('set-water-goal-btn');
    const suggestedGoalText = document.getElementById('suggested-water-goal-text');
    const suggestedGoalValue = document.getElementById('suggested-goal-value');

    const weeklyStatsContent = document.getElementById('weekly-stats-content');
    const weeklyChartCanvas = document.getElementById('waterWeeklyChart');
    const waterChartError = document.getElementById('water-chart-error');

    const DEFAULT_WATER_GOAL_ML = 2500;
    const ML_PER_KG_WEIGHT_MIN = 30;
    const ML_PER_KG_WEIGHT_MAX = 35;
    let currentDailyWaterGoal = DEFAULT_WATER_GOAL_ML;
    let currentlyEditingWaterLogId = null;
    let weeklyWaterChartInstance = null;

    if (!addWaterBtn || !logDatePicker || !waterHistoryListUl || !setGoalBtn || !weeklyChartCanvas) {
        console.error("Water tracker page essential elements not found. Initialization failed.");
        return;
    }
    if (addWaterBtn.hasWaterListener) {
        console.log("Water tracker listeners seem to exist. Re-initializing data.");
        loadUserWaterGoalAndSuggest().then(() => {
            loadLogForDate(logDatePicker.value || formatDate(new Date()));
            loadWeeklyStatsAndChart();
        });
        return;
    }

    // --- Helper Functions ---
    const formatDate = (dateObj) => {
        const d = dateObj instanceof Date ? dateObj : new Date(dateObj); // Handle string or Date
        const month = '' + (d.getMonth() + 1);
        const day = '' + d.getDate();
        const year = d.getFullYear();
        return [year, month.padStart(2, '0'), day.padStart(2, '0')].join('-');
    };
    const formatTimeForInput = (dateObj) => {
        const d = dateObj instanceof Date ? dateObj : new Date(dateObj);
        const hours = ('' + d.getHours()).padStart(2, '0');
        const minutes = ('' + d.getMinutes()).padStart(2, '0');
        return `${hours}:${minutes}`;
    };
    const formatDisplayTime = (isoStringOrDate) => new Date(isoStringOrDate).toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit', hour12: true });

    // --- Goal Management ---
    const loadUserWaterGoalAndSuggest = async () => { /* ... (Same as before, ensure summaryDailyWaterGoalSpan is updated) ... */
        try {
            const goalSetting = await app.getUserProfileSetting('waterDailyGoal');
            currentDailyWaterGoal = (goalSetting && goalSetting.value) ? parseInt(goalSetting.value, 10) : DEFAULT_WATER_GOAL_ML;
        } catch (error) {
            console.error("Error loading water goal, using default:", error);
            currentDailyWaterGoal = DEFAULT_WATER_GOAL_ML;
        }
        currentGoalDisplayStrong.textContent = currentDailyWaterGoal;
        customGoalInput.value = currentDailyWaterGoal;
        summaryDailyWaterGoalSpan.textContent = currentDailyWaterGoal; // Update summary display here too
        await suggestGoalBasedOnWeight();
    };
    const suggestGoalBasedOnWeight = async () => { /* ... (Same as before) ... */
        let latestWeightKg = null; suggestedGoalText.classList.add('hidden'); // Hide initially
        try {
            const calorieRecords = await app.getAllData(app.calorieStoreName);
            if (calorieRecords && calorieRecords.length > 0) { latestWeightKg = calorieRecords.sort((a,b) => new Date(b.date) - new Date(a.date))[0].weight; }
            if (!latestWeightKg) { const bmiRecords = await app.getAllData(app.bmiStoreName); if (bmiRecords && bmiRecords.length > 0) { latestWeightKg = bmiRecords.sort((a,b) => new Date(b.date) - new Date(a.date))[0].weight; } }
            if (latestWeightKg) {
                const suggestedMin = Math.round(latestWeightKg * ML_PER_KG_WEIGHT_MIN); const suggestedMax = Math.round(latestWeightKg * ML_PER_KG_WEIGHT_MAX);
                suggestedGoalValue.textContent = `${suggestedMin} - ${suggestedMax}`; suggestedGoalText.classList.remove('hidden');
            } else { suggestedGoalValue.textContent = 'للاقتراح، أدخل وزنك في حاسبة BMI/السعرات'; suggestedGoalText.classList.remove('hidden'); }
        } catch (error) { console.error("Error suggesting water goal:", error); suggestedGoalValue.textContent = 'خطأ'; suggestedGoalText.classList.remove('hidden');}
    };
    const handleSetUserGoal = async () => { /* ... (Same as before, ensure summary & chart refresh) ... */
        const newGoal = parseInt(customGoalInput.value, 10);
        if (isNaN(newGoal) || newGoal <= 0) { app.showToast("الرجاء إدخال هدف صحيح."); return; }
        try {
            await app.putUserProfileSetting({ setting: 'waterDailyGoal', value: newGoal });
            currentDailyWaterGoal = newGoal; currentGoalDisplayStrong.textContent = newGoal; summaryDailyWaterGoalSpan.textContent = newGoal;
            app.showToast(`تم تحديث هدف الماء إلى ${newGoal} مل.`);
            loadLogForDate(logDatePicker.value || formatDate(new Date())); // Refresh current day's summary & progress
            loadWeeklyStatsAndChart(); // Refresh weekly stats chart as goal changed
        } catch (error) { app.showToast("فشل حفظ الهدف."); }
    };
    setGoalBtn.addEventListener('click', handleSetUserGoal);

    // --- UI Updates & Log Rendering ---
    const updateDailySummaryUI = (totalConsumed) => { /* ... (Same interactive glass logic) ... */
        totalWaterTodaySpan.textContent = totalConsumed; summaryDailyWaterGoalSpan.textContent = currentDailyWaterGoal;
        const percentage = currentDailyWaterGoal > 0 ? Math.min((totalConsumed / currentDailyWaterGoal) * 100, 100) : 0;
        waterLevelIndicator.style.height = `${percentage}%`; waterPercentageText.textContent = `${Math.round(percentage)}%`;
        waterLevelIndicator.classList.toggle('show-percentage', percentage > 10);
        if (percentage >= 100) waterLevelIndicator.style.background = 'linear-gradient(to top, #4CAF50, #8BC34A)';
        else if (percentage > 70) waterLevelIndicator.style.background = 'linear-gradient(to top, #2196F3, #64B5F6)';
        else if (percentage > 30) waterLevelIndicator.style.background = 'linear-gradient(to top, #64B5F6, #90CAF9)';
        else waterLevelIndicator.style.background = 'linear-gradient(to top, #90CAF9, #BBDEFB)';
    };

    const addWaterRowToTable = (record) => {
        const li = document.createElement('li');
        li.setAttribute('data-log-id', record.id);
        li.innerHTML = `
            <div class="water-log-entry-main">
                <div class="water-entry-info">
                    <span class="water-entry-time">${formatDisplayTime(record.timestamp)}</span>
                    <span class="water-entry-amount">${record.amount} مل</span>
                </div>
                <div class="water-log-actions">
                    <button class="edit-water-btn" title="تعديل"><i class="fas fa-edit"></i></button>
                    <button class="delete-water-btn" title="حذف"><i class="fas fa-times-circle"></i></button>
                </div>
            </div>
            ${record.notes ? `<p class="water-entry-notes">${escapeHtml(record.notes)}</p>` : ''}
        `;
        li.querySelector('.delete-water-btn').addEventListener('click', () => deleteWaterLog(record.id, li));
        li.querySelector('.edit-water-btn').addEventListener('click', () => populateFormForEdit(record));
        const placeholderLi = waterHistoryListUl.querySelector('.no-entries');
        if (placeholderLi) placeholderLi.remove();
        waterHistoryListUl.prepend(li);
    };
     const escapeHtml = (unsafe) => unsafe.replace(/[&<"']/g, (match) => ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;',"'":'&#039;'})[match]);


    const loadLogForDate = async (dateString) => { /* ... (Same logic, ensure updateDailySummaryUI is called) ... */
        if (!dateString) dateString = formatDate(new Date());
        summaryDateSpan.textContent = new Date(dateString + 'T00:00:00').toLocaleDateString('ar-EG', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
        try {
            const logsForDay = await app.getDataByIndex(app.waterLogStoreName, 'date', dateString);
            let totalConsumed = 0; waterHistoryListUl.innerHTML = '';
            if (logsForDay.length === 0) { waterHistoryListUl.innerHTML = '<li class="no-entries">لم يتم تسجيل أي كميات لهذا اليوم.</li>'; }
            else { logsForDay.sort((a,b) => new Date(b.timestamp) - new Date(a.timestamp)).forEach(log => { addWaterRowToTable(log); totalConsumed += log.amount; }); }
            updateDailySummaryUI(totalConsumed);
        } catch (error) { console.error(`Error loading water log for ${dateString}:`, error); waterHistoryListUl.innerHTML = '<li class="no-entries" style="color:red;">خطأ تحميل السجل.</li>'; updateDailySummaryUI(0);}
    };

    // --- Add/Edit/Delete Water ---
    const resetWaterForm = () => {
        waterAmountInput.value = '';
        waterNotesInput.value = ''; // Clear notes
        waterTimeInput.value = formatTimeForInput(new Date()); // Reset time to now
        // waterDateInput.value = formatDate(new Date()); // Keep date or reset as needed
        waterErrorMsg.textContent = '';
        addWaterBtn.classList.remove('hidden');
        updateWaterBtn.classList.add('hidden');
        cancelEditWaterBtn.classList.add('hidden');
        waterFormTitle.innerHTML = '<i class="fas fa-plus-circle"></i> إضافة كمية ماء';
        currentlyEditingWaterLogId = null;
    };

    const populateFormForEdit = (logEntry) => {
        currentlyEditingWaterLogId = logEntry.id;
        waterDateInput.value = logEntry.date;
        waterTimeInput.value = formatTimeForInput(new Date(logEntry.timestamp)); // Set time from timestamp
        waterAmountInput.value = logEntry.amount;
        waterNotesInput.value = logEntry.notes || '';
        waterFormTitle.innerHTML = '<i class="fas fa-edit"></i> تعديل السجل';
        addWaterBtn.classList.add('hidden');
        updateWaterBtn.classList.remove('hidden');
        cancelEditWaterBtn.classList.remove('hidden');
        waterAmountInput.focus();
    };
    cancelEditWaterBtn.addEventListener('click', resetWaterForm);

    const handleAddOrUpdateWater = async (isUpdate = false) => {
        waterErrorMsg.textContent = '';
        const date = waterDateInput.value || formatDate(new Date());
        const time = waterTimeInput.value || formatTimeForInput(new Date());
        const amount = parseInt(waterAmountInput.value, 10);
        const notes = waterNotesInput.value.trim();

        if (isNaN(amount) || amount <= 0) { waterErrorMsg.textContent = 'الرجاء إدخال كمية صحيحة.'; return; }
        if (!date || !time) { waterErrorMsg.textContent = 'الرجاء إدخال التاريخ والوقت.'; return; }

        // Combine date and time to create a full timestamp
        const [year, month, day] = date.split('-');
        const [hours, minutes] = time.split(':');
        const timestamp = new Date(year, month - 1, day, hours, minutes).toISOString();

        const waterLogEntry = { date, amount, notes, timestamp };

        try {
            if (isUpdate && currentlyEditingWaterLogId) {
                waterLogEntry.id = currentlyEditingWaterLogId;
                await app.putData(app.waterLogStoreName, waterLogEntry); // Use putData for updates
                app.showToast("تم تحديث سجل الماء بنجاح!");
            } else {
                await app.saveData(app.waterLogStoreName, waterLogEntry);
                app.showToast(`تم تسجيل ${amount} مل ماء بنجاح!`);
            }
            resetWaterForm();
            loadLogForDate(logDatePicker.value); // Refresh current day's log
            loadWeeklyStatsAndChart();      // Refresh weekly stats & chart
        } catch (error) {
            console.error("Error saving/updating water log:", error);
            waterErrorMsg.textContent = 'خطأ في حفظ السجل.';
        }
    };
    addWaterBtn.addEventListener('click', () => handleAddOrUpdateWater(false));
    updateWaterBtn.addEventListener('click', () => handleAddOrUpdateWater(true));
    quickAddButtons.forEach(button => {
        button.addEventListener('click', () => {
            waterAmountInput.value = button.dataset.amount;
            waterTimeInput.value = formatTimeForInput(new Date()); // Set current time for quick add
            waterNotesInput.value = ''; // Clear notes for quick add
            handleAddOrUpdateWater(false); // Directly add it
        });
    });

    const deleteWaterLog = async (logId, listItemElement) => { /* ... (Same, ensure loadWeeklyStatsAndChart is called) ... */
        if (!confirm("متأكد من الحذف؟")) return;
        try {
            await app.deleteData(app.waterLogStoreName, logId);
            listItemElement.remove(); app.showToast("تم حذف سجل الماء.");
            loadLogForDate(logDatePicker.value);
            loadWeeklyStatsAndChart(); // Refresh stats & chart
        } catch (error) { console.error("Error deleting water log:", error); alert("فشل حذف السجل.");}
    };
    logDatePicker.addEventListener('change', (e) => loadLogForDate(e.target.value));

    // --- Weekly Stats & Chart Functions ---
    const renderWeeklyWaterChart = (chartData) => {
        if (!weeklyChartCanvas) return;
        const ctx = weeklyChartCanvas.getContext('2d');
        if (weeklyWaterChartInstance) { weeklyWaterChartInstance.destroy(); }

        const isDarkMode = document.body.getAttribute('data-theme') === 'dark';
        const gridColor = isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)';
        const labelColor = isDarkMode ? '#ccc' : '#555';
        const barColor = 'rgba(54, 162, 235, 0.6)'; // Blue for bars
        const barBorderColor = 'rgba(54, 162, 235, 1)';
        const goalLineColor = isDarkMode ? 'rgba(255, 206, 86, 0.8)' : 'rgba(255, 159, 64, 0.8)'; // Orange/Yellow for goal line

        weeklyWaterChartInstance = new Chart(ctx, {
            type: 'bar', // Bar chart for daily intake
            data: {
                labels: chartData.labels, // Array of date strings (last 7 days)
                datasets: [
                    {
                        label: 'الماء المستهلك (مل)',
                        data: chartData.consumedData,
                        backgroundColor: barColor,
                        borderColor: barBorderColor,
                        borderWidth: 1,
                        order: 2 // Ensure bars are behind the line
                    },
                    {
                        label: 'الهدف اليومي (مل)',
                        data: chartData.goalData, // Array of goal values for each day
                        borderColor: goalLineColor,
                        backgroundColor: goalLineColor,
                        type: 'line', // Render goal as a line
                        fill: false,
                        tension: 0.1,
                        pointRadius: 3,
                        order: 1 // Line on top
                    }
                ]
            },
            options: {
                responsive: true, maintainAspectRatio: false,
                scales: {
                    y: { beginAtZero: true, title: { display: true, text: 'كمية الماء (مل)', color: labelColor }, ticks: { color: labelColor }, grid: { color: gridColor } },
                    x: { title: { display: true, text: 'التاريخ', color: labelColor }, ticks: { color: labelColor }, grid: { display: false } } // No vertical grid lines for cleaner bar chart
                },
                plugins: { legend: { labels: { color: labelColor } }, tooltip: { titleFont: { family: 'Cairo'}, bodyFont: { family: 'Cairo'} } }
            }
        });
    };

    const loadWeeklyStatsAndChart = async () => {
        weeklyStatsContent.innerHTML = '<p class="loading-stats">يتم تحميل الإحصائيات...</p>';
        waterChartError.classList.add('hidden');
        try {
            const today = new Date();
            let totalIntakeLast7Days = 0;
            let daysGoalMet = 0;
            let daysWithData = 0;
            let highestIntake = 0;
            let lowestIntake = Infinity;

            const chartLabels = [];
            const chartConsumedData = [];
            const chartGoalData = [];

            // Ensure daily goal is loaded before calculating stats
            if (!currentDailyWaterGoal || currentDailyWaterGoal === DEFAULT_WATER_GOAL_ML) {
                 // Try to load it again if it's still the default or null
                 const goalSetting = await app.getUserProfileSetting('waterDailyGoal');
                 if (goalSetting && goalSetting.value) {
                     currentDailyWaterGoal = parseInt(goalSetting.value, 10);
                 }
            }
            const effectiveGoal = currentDailyWaterGoal; // Goal for the week

            for (let i = 6; i >= 0; i--) { // Iterate from 6 days ago up to today
                const date = new Date(today);
                date.setDate(today.getDate() - i);
                const dateString = formatDate(date);
                chartLabels.push(date.toLocaleDateString('ar-EG', { day: 'numeric', month: 'short' }));

                const logsForDay = await app.getDataByIndex(app.waterLogStoreName, 'date', dateString);
                let dailyTotal = 0;
                logsForDay.forEach(log => dailyTotal += log.amount);

                chartConsumedData.push(dailyTotal);
                chartGoalData.push(effectiveGoal);

                if (logsForDay.length > 0) {
                    totalIntakeLast7Days += dailyTotal;
                    daysWithData++;
                    if (dailyTotal > highestIntake) highestIntake = dailyTotal;
                    if (dailyTotal < lowestIntake) lowestIntake = dailyTotal;
                }
                if (dailyTotal >= effectiveGoal) {
                    daysGoalMet++;
                }
            }
            const averageIntake = daysWithData > 0 ? Math.round(totalIntakeLast7Days / daysWithData) : 0;
            if (lowestIntake === Infinity) lowestIntake = 0; // If no data, lowest is 0

            weeklyStatsContent.innerHTML = `
                <p>متوسط الاستهلاك اليومي (لـ ${daysWithData} أيام مسجلة): <strong>${averageIntake}</strong> مل</p>
                <p>عدد أيام تحقيق الهدف: <strong>${daysGoalMet}</strong> من 7 أيام</p>
                <p>أعلى كمية مسجلة في يوم: <strong>${highestIntake}</strong> مل</p>
                <p>أقل كمية مسجلة في يوم (بوجود تسجيلات): <strong>${lowestIntake}</strong> مل</p>
            `;

            if (daysWithData === 0) { // No data at all for the chart
                waterChartError.textContent = "لا توجد بيانات كافية لعرض الرسم البياني الأسبوعي.";
                waterChartError.classList.remove('hidden');
                weeklyChartCanvas.style.display = 'none';
                if(weeklyWaterChartInstance) weeklyWaterChartInstance.destroy();
            } else {
                weeklyChartCanvas.style.display = 'block';
                renderWeeklyWaterChart({ labels: chartLabels, consumedData: chartConsumedData, goalData: chartGoalData });
            }

        } catch (error) {
            console.error("Error loading weekly stats/chart:", error);
            weeklyStatsContent.innerHTML = '<p style="color:red;">خطأ تحميل الإحصائيات.</p>';
            waterChartError.textContent = "خطأ تحميل بيانات الرسم البياني.";
            waterChartError.classList.remove('hidden');
        }
    };

    // --- Initialization ---
    const initializePage = async () => {
        const todayStr = formatDate(new Date());
        waterDateInput.value = todayStr;
        waterTimeInput.value = formatTimeForInput(new Date()); // Set initial time
        logDatePicker.value = todayStr;
        await loadUserWaterGoalAndSuggest();
        await loadLogForDate(todayStr);
        await loadWeeklyStatsAndChart();
        resetWaterForm(); // Ensure add/update buttons are in correct initial state
    };

    initializePage();
    addWaterBtn.hasWaterListener = true; // Set flag after all initial loads
    console.log("Enhanced Water Tracker Page Module Initialized Successfully.");
}
window.currentPageInitializer = initializeWaterPageModule;