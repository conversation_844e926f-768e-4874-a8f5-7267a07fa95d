<div class="tracker-container page-content">
    <h2><i class="fas fa-apple-alt"></i> متتبع السعرات الحرارية والوجبات</h2>
    <p class="description">سجل وجباتك اليومية وتتبع استهلاكك من السعرات والمغذيات بدقة.</p>

    <div class="daily-goals-card calculator-card">
        <h3><i class="fas fa-bullseye"></i> أهدافك اليومية</h3>
        <div id="daily-goals-content"><p>يتم تحميل الأهداف...</p></div>
        <p class="tip-to-set-goals hidden" id="set-goals-tip">
            <i class="fas fa-info-circle"></i> لم يتم تعيين أهدافك. قم بتعيينها من <a href="#calories" class="nav-link-inline">حاسبة السعرات</a>.
        </p>
    </div>

    <div class="tracker-layout">
        <div class="add-meal-card calculator-card">
            <h3><i class="fas fa-plus-circle"></i> إضافة وجبة جديدة</h3>
            <div class="input-group"><label for="meal-date">تاريخ الوجبة:</label><input type="date" id="meal-date"></div>
            <div class="input-group"><label for="meal-type">نوع الوجبة:</label><select id="meal-type"><option value="breakfast">فطور</option><option value="lunch">غداء</option><option value="dinner">عشاء</option><option value="snack">وجبة خفيفة</option></select></div>
            <div class="input-group"><label for="food-name">اسم الطعام:</label><input type="text" id="food-name" placeholder="مثال: صدر دجاج مشوي"></div>
            <div class="input-group"><label for="food-quantity">الكمية/الحصة:</label><input type="text" id="food-quantity" placeholder="مثال: 100 جرام"></div>
            <div class="input-row"><div class="input-group"><label for="meal-calories">السعرات:</label><input type="number" id="meal-calories" placeholder="0"></div><div class="input-group"><label for="meal-protein">بروتين (جم):</label><input type="number" id="meal-protein" placeholder="0"></div></div>
            <div class="input-row"><div class="input-group"><label for="meal-carbs">كارب (جم):</label><input type="number" id="meal-carbs" placeholder="0"></div><div class="input-group"><label for="meal-fat">دهون (جم):</label><input type="number" id="meal-fat" placeholder="0"></div></div>
            <button id="add-meal-btn"><i class="fas fa-utensils"></i> إضافة الوجبة</button>
            <button id="update-meal-btn" class="secondary-button hidden"><i class="fas fa-edit"></i> تحديث الوجبة</button>
            <button id="cancel-edit-btn" class="danger-button hidden" style="margin-top:10px;"><i class="fas fa-times"></i> إلغاء التعديل</button>
            <p id="meal-error" class="error-message"></p>
        </div>

        <div class="daily-log-card calculator-card">
            <h3><i class="fas fa-calendar-day"></i> سجل اليوم</h3>
            <div class="input-group"><label for="log-date-picker">اختر تاريخ لعرض السجل:</label><input type="date" id="log-date-picker"></div>
            <div id="daily-summary">
                <h4>ملخص اليوم (<span id="summary-date">--</span>):</h4>
                <div class="summary-item"><label>السعرات:</label><div class="progress-bar-container"><div class="progress-bar" id="calories-progress"></div></div><span id="calories-summary-text">0 / 0</span></div>
                <div class="summary-item"><label>البروتين:</label><div class="progress-bar-container"><div class="progress-bar protein-bar" id="protein-progress"></div></div><span id="protein-summary-text">0 / 0 جم</span></div>
                <div class="summary-item"><label>الكربوهيدرات:</label><div class="progress-bar-container"><div class="progress-bar carb-bar" id="carbs-progress"></div></div><span id="carbs-summary-text">0 / 0 جم</span></div>
                <div class="summary-item"><label>الدهون:</label><div class="progress-bar-container"><div class="progress-bar fat-bar" id="fat-progress"></div></div><span id="fat-summary-text">0 / 0 جم</span></div>
            </div>
            <div class="meals-list-container"><h4>الوجبات المسجلة:</h4><ul id="meals-list"><li class="no-meals">لم يتم تسجيل أي وجبات لهذا اليوم.</li></ul></div>
        </div>
    </div>

    <div class="progress-chart-card calculator-card">
        <h3><i class="fas fa-chart-line"></i> ملخص التقدم (آخر 7 أيام)</h3>
        <div class="chart-container" style="position: relative; height:350px; width:100%;">
            <canvas id="calorieProgressChart"></canvas>
        </div>
        <p id="chart-error-message" class="error-message hidden" style="text-align: center; margin-top:15px;"></p>
    </div>
    </div>