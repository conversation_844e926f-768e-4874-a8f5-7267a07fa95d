/* BMI Page Specific Styles */

.bmi-container { max-width: 800px; margin: 0 auto; /* Removed top margin */ }
.bmi-container h2 { text-align: center; color: var(--primary-color); margin-bottom: 30px; font-size: 1.8rem; }
.bmi-calculator-card { background-color: var(--card-background); padding: 35px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05); border: 1px solid var(--border-color); margin-bottom: 30px; }
.input-group { margin-bottom: 25px; }
.input-group label { display: flex; align-items: center; margin-bottom: 10px; font-weight: 600; color: var(--text-color); font-size: 1.1rem; }
.input-group label i { margin-left: 10px; color: var(--primary-color); font-size: 1.2rem; }
.input-group input { width: 100%; padding: 15px 20px; border: 1px solid var(--border-color); border-radius: 8px; font-size: 1.1rem; background-color: var(--background-color); color: var(--text-color); transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out; text-align: right; font-family: 'Cairo', sans-serif; }
.input-group input::placeholder { color: #aaa; }
.input-group input:focus { outline: none; border-color: var(--secondary-color); box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2); }

#calculate-bmi-btn { width: 100%; padding: 16px; border: none; border-radius: 8px; font-size: 1.3rem; cursor: pointer; transition: background-color 0.3s ease-in-out, transform 0.1s ease; font-weight: 700; display: flex; justify-content: center; align-items: center; background-color: var(--primary-color); color: #fff; box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3); }
#calculate-bmi-btn:hover { background-color: var(--secondary-color); }
#calculate-bmi-btn i { margin-left: 10px; }
#calculate-bmi-btn:active { transform: scale(0.98); }

/* BMI Result Area */
.bmi-result-area { background-color: var(--card-background); padding: 30px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05); border: 1px solid var(--border-color); text-align: center; min-height: 180px; display: flex; flex-direction: column; justify-content: center; align-items: center; transition: border-color var(--transition-speed); margin-bottom: 30px; }
.initial-message { color: #888; font-size: 1.1rem; }
.bmi-value { font-size: 3.5rem; font-weight: 700; line-height: 1.1; margin-bottom: 5px; transition: color var(--transition-speed); }
.bmi-category { font-size: 1.5rem; font-weight: 600; text-transform: uppercase; letter-spacing: 1px; padding: 5px 15px; border-radius: 20px; color: #fff; transition: background-color var(--transition-speed); margin-bottom: 15px; }
.bmi-range-info { font-size: 0.95rem; color: #666; }
[data-theme="dark"] .bmi-range-info { color: #aaa; }

/* Result Colors */
:root {
    --bmi-underweight-color: #2196F3;
    --bmi-normal-color: #4CAF50;
    --bmi-overweight-color: #FF9800;
    --bmi-obese-color: #f44336;
}
[data-theme="dark"] {
    --bmi-underweight-color: #64B5F6;
    --bmi-normal-color: #81C784;
    --bmi-overweight-color: #FFB74D;
    --bmi-obese-color: #E57373;
}
.bmi-result-area.underweight { border-top: 5px solid var(--bmi-underweight-color); }
.bmi-result-area.normal { border-top: 5px solid var(--bmi-normal-color); }
.bmi-result-area.overweight { border-top: 5px solid var(--bmi-overweight-color); }
.bmi-result-area.obese { border-top: 5px solid var(--bmi-obese-color); }
.bmi-value.underweight { color: var(--bmi-underweight-color); }
.bmi-value.normal { color: var(--bmi-normal-color); }
.bmi-value.overweight { color: var(--bmi-overweight-color); }
.bmi-value.obese { color: var(--bmi-obese-color); }
.bmi-category.underweight { background-color: var(--bmi-underweight-color); }
.bmi-category.normal { background-color: var(--bmi-normal-color); }
.bmi-category.overweight { background-color: var(--bmi-overweight-color); }
.bmi-category.obese { background-color: var(--bmi-obese-color); }

/* BMI Tips */
.bmi-tips { margin-top: 0; margin-bottom: 30px; padding: 20px; background-color: var(--background-color); border: 1px solid var(--border-color); border-radius: 8px; color: var(--text-color); display: none; }
.bmi-tips.show { display: block; }
.bmi-tips h4 { margin-bottom: 15px; color: var(--primary-color); font-weight: 700; }
.bmi-tips ul { list-style: none; padding-right: 0; }
.bmi-tips ul li { margin-bottom: 10px; position: relative; padding-right: 25px; line-height: 1.6; }
.bmi-tips ul li::before { content: "\f14a"; font-family: "Font Awesome 6 Free"; font-weight: 900; color: var(--primary-color); position: absolute; right: 0; top: 4px; }

/* History Table */
.history-container { margin-top: 40px; background-color: var(--card-background); padding: 25px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05); border: 1px solid var(--border-color); }
.history-container h3 { text-align: center; color: var(--primary-color); margin-bottom: 20px; font-size: 1.5rem; }
.history-container h3 i { margin-left: 8px; }
.table-wrapper { overflow-x: auto; }
#bmi-history-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
#bmi-history-table th, #bmi-history-table td { border: 1px solid var(--border-color); padding: 12px 15px; text-align: center; vertical-align: middle; }
#bmi-history-table th { background-color: var(--primary-color); color: #fff; font-weight: 600; }
#bmi-history-table tbody tr:nth-child(even) { background-color: var(--background-color); }
#bmi-history-table tbody tr:hover { background-color: var(--secondary-color); color: #fff; transition: background-color 0.2s ease; }
[data-theme="dark"] #bmi-history-table tbody tr:hover { background-color: #333; }
.delete-row-btn { background-color: var(--delete-color); color: #fff; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; font-size: 0.9rem; transition: background-color 0.2s ease; }
.delete-row-btn:hover { background-color: #c62828; }
[data-theme="dark"] .delete-row-btn:hover { background-color: #b71c1c; }

/* Responsive */
@media (max-width: 768px) {
    .bmi-calculator-card, .bmi-result-area, .history-container { padding: 20px; }
    .bmi-value { font-size: 2.8rem; }
    .bmi-category { font-size: 1.2rem; }
    #bmi-history-table th, #bmi-history-table td { padding: 8px 10px; font-size: 0.9rem; }
}