<div class="water-tracker-container page-content">
    <h2><i class="fas fa-tint"></i> متتبع شرب الماء اليومي</h2>
    <p class="description">سجل كمية الماء التي تشربها وحافظ على رطوبة جسمك بفعالية.</p>

    <div class="water-goal-card calculator-card">
        <h3><i class="fas fa-bullseye"></i> هدفك اليومي من الماء</h3>
        <div class="current-goal-display">
            <span>هدفك الحالي:</span>
            <strong id="current-daily-water-goal">2500</strong> مل
        </div>
        <div class="input-group">
            <label for="custom-water-goal">تحديث الهدف (مل):</label>
            <input type="number" id="custom-water-goal" placeholder="مثال: 3000">
        </div>
        <button id="set-water-goal-btn"><i class="fas fa-save"></i> حفظ الهدف الجديد</button>
        <p id="suggested-water-goal-text" class="subtle-text hidden">الهدف المقترح بناءً على وزنك: <strong id="suggested-goal-value">--</strong> مل</p>
    </div>

    <div class="water-layout">
        <div class="water-input-card calculator-card">
            <h3 id="water-form-title"><i class="fas fa-plus-circle"></i> إضافة كمية ماء</h3>
            <div class="input-group">
                <label for="water-date">التاريخ:</label>
                <input type="date" id="water-date">
            </div>
            <div class="input-group">
                <label for="water-time">الوقت:</label> <input type="time" id="water-time">
            </div>
            <div class="input-group">
                <label for="water-amount">الكمية (مل):</label>
                <input type="number" id="water-amount" placeholder="250">
            </div>
            <div class="input-group">
                <label for="water-notes">ملاحظات (اختياري):</label>
                <textarea id="water-notes" rows="2" placeholder="مثال: بعد التمرين"></textarea>
            </div>
            <div class="quick-add-buttons">
                <button class="quick-add-btn" data-amount="250">250 مل</button>
                <button class="quick-add-btn" data-amount="500">500 مل</button>
                <button class="quick-add-btn" data-amount="750">750 مل</button>
                <button class="quick-add-btn" data-amount="1000">1 لتر</button>
            </div>
            <button id="add-water-btn"><i class="fas fa-tint"></i> تسجيل الكمية</button>
            <button id="update-water-btn" class="secondary-button hidden"><i class="fas fa-edit"></i> تحديث السجل</button>
            <button id="cancel-edit-water-btn" class="danger-button hidden" style="margin-top:10px;"><i class="fas fa-times"></i> إلغاء</button>
            <p id="water-error" class="error-message"></p>
        </div>

        <div class="water-summary-history-card calculator-card">
            <h3><i class="fas fa-calendar-day"></i> ملخص وسجل اليوم</h3>
            <div class="input-group">
                <label for="log-water-date-picker">عرض سجل تاريخ:</label>
                <input type="date" id="log-water-date-picker">
            </div>
            <div class="daily-water-summary">
                <h4>ملخص (<span id="water-summary-date">--</span>):</h4>
                <div class="water-progress-visual">
                    <div class="water-glass"><div class="water-level" id="water-level-indicator"><span class="water-percentage" id="water-percentage-text">0%</span></div></div>
                    <div class="summary-text"><p>الإجمالي: <strong id="total-water-today">0</strong> مل</p><p>الهدف: <strong id="summary-daily-water-goal">2500</strong> مل</p></div>
                </div>
            </div>
            <div class="water-history-list-container">
                <h4>السجل التفصيلي:</h4>
                <ul id="water-history-list"><li class="no-entries">لم يتم تسجيل أي كميات لهذا اليوم.</li></ul>
            </div>
        </div>
    </div>

    <div class="weekly-stats-card calculator-card">
        <h3><i class="fas fa-chart-bar"></i> إحصائيات آخر 7 أيام</h3>
        <div id="weekly-stats-content"><p class="loading-stats">يتم تحميل الإحصائيات...</p></div>
        <div class="chart-container" style="position: relative; height:300px; width:100%; margin-top:20px;">
            <canvas id="waterWeeklyChart"></canvas>
        </div>
        <p id="water-chart-error" class="error-message hidden" style="text-align: center; margin-top:15px;"></p>
    </div>
</div>