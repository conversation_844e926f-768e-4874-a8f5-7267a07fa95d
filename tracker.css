/* tracker.css - Styles for Calorie & Meal Tracker Page */

.tracker-container {
    max-width: 1200px; /* Wider container for layout */
    margin: 0 auto;
}

.tracker-container h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.8rem;
}
.tracker-container h2 i { margin-left: 10px; }
.tracker-container .description { text-align: center; color: #888; margin-bottom: 30px; font-size: 1.1rem; }

.calculator-card { /* Reused general card styling */
    background-color: var(--card-background);
    padding: 25px 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
    margin-bottom: 30px;
}
.calculator-card h3 {
    text-align: center;
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 25px;
    font-size: 1.5rem;
}
.calculator-card h3 i { margin-left: 8px; }

/* Daily Goals Display */
.daily-goals-card { border-top: 5px solid var(--secondary-color); }
#daily-goals-content { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; text-align: center; }
#daily-goals-content .goal-item { background-color: var(--background-color); padding: 15px; border-radius: 8px; border: 1px solid var(--border-color); }
#daily-goals-content .goal-item span { display: block; font-size: 0.9rem; color: #777; margin-bottom: 5px; }
[data-theme="dark"] #daily-goals-content .goal-item span { color: #bbb; }
#daily-goals-content .goal-item strong { font-size: 1.6rem; color: var(--primary-color); font-weight: 700; }
.tip-to-set-goals { text-align: center; margin-top: 20px; padding: 10px; background-color: var(--secondary-color-light, #e3f2fd); color: var(--primary-color); border: 1px solid var(--secondary-color); border-radius: 8px; }
[data-theme="dark"] .tip-to-set-goals { background-color: rgba(66, 165, 245, 0.2); } /* Lighter secondary for dark */
.tip-to-set-goals.hidden { display: none; }
.nav-link-inline { color: var(--primary-color); text-decoration: underline; font-weight: 600; }

/* Layout for Add Meal & Daily Log */
.tracker-layout { display: grid; grid-template-columns: 1fr 1.5fr; gap: 30px; align-items: flex-start; }

.input-row { display: flex; gap: 20px; margin-bottom: 20px; }
.input-row .input-group { flex: 1; margin-bottom: 0; }
.input-group { margin-bottom: 20px; }
.input-group label { display: block; margin-bottom: 8px; font-weight: 600; color: var(--text-color); }
.input-group input[type="text"], .input-group input[type="number"], .input-group input[type="date"], .input-group select { width: 100%; padding: 12px 15px; border: 1px solid var(--border-color); border-radius: 8px; font-size: 1rem; background-color: var(--background-color); color: var(--text-color); font-family: 'Cairo', sans-serif; }
.input-group input:focus, .input-group select:focus { outline: none; border-color: var(--secondary-color); box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2); }
.error-message { color: var(--delete-color); text-align: center; margin-top: 10px; font-weight: 600; min-height: 1em; }
#add-meal-btn, #update-meal-btn, #cancel-edit-btn { width: 100%; padding: 14px; border: none; border-radius: 8px; font-size: 1.1rem; cursor: pointer; transition: background-color 0.3s; font-weight: 600; display: flex; justify-content: center; align-items: center; background-color: var(--primary-color); color: #fff; margin-top: 10px; }
#add-meal-btn i, #update-meal-btn i, #cancel-edit-btn i { margin-left: 8px; } /* RTL: margin-right */
#add-meal-btn:hover { background-color: var(--secondary-color); }
.secondary-button { background-color: var(--secondary-color) !important; }
.secondary-button:hover { background-color: var(--primary-color) !important; }
.danger-button { background-color: #6c757d !important; }
.danger-button:hover { background-color: #5a6268 !important; }
.hidden { display: none !important; }

/* Daily Log Section Specifics */
.daily-log-card #log-date-picker { margin-bottom: 25px; }
#daily-summary { margin-bottom: 30px; }
#daily-summary h4 { font-size: 1.2rem; color: var(--secondary-color); margin-bottom: 15px; text-align: right; border-bottom: 1px solid var(--border-color); padding-bottom: 10px; }
.summary-item { margin-bottom: 15px; }
.summary-item label { font-size: 0.95rem; color: var(--text-color); display: block; margin-bottom: 5px; }
.progress-bar-container { width: 100%; background-color: var(--border-color); border-radius: 10px; height: 20px; overflow: hidden; margin-bottom: 5px; }
.progress-bar { height: 100%; background-color: var(--primary-color); border-radius: 0 10px 10px 0; /* RTL fix */ transition: width 0.5s ease-in-out; text-align: center; color: white; font-size: 0.8rem; line-height: 20px; }
.progress-bar.protein-bar { background-color: #f44336; }
.progress-bar.carb-bar { background-color: #FF9800; }
.progress-bar.fat-bar { background-color: #9C27B0; }
.summary-item span { font-size: 0.9rem; color: #555; display: block; text-align: left; }
[data-theme="dark"] .summary-item span { color: #ccc; }

.meals-list-container h4 { font-size: 1.2rem; color: var(--secondary-color); margin-bottom: 15px; text-align: right; border-bottom: 1px solid var(--border-color); padding-bottom: 10px; }
#meals-list { list-style: none; padding: 0; max-height: 400px; overflow-y: auto; }

/* --- Improved Meal Item Styling --- */
#meals-list li {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 15px;
    box-shadow: 0 3px 6px rgba(0,0,0,0.08);
    transition: box-shadow 0.3s ease;
}
#meals-list li:hover {
    box-shadow: 0 5px 12px rgba(0,0,0,0.12);
}
#meals-list li.no-meals { text-align: center; color: #888; padding: 20px; background-color: transparent; border: 1px dashed var(--border-color); box-shadow: none; }

.meal-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}
.meal-item-header strong { /* Food Name */
    font-size: 1.25rem;
    color: var(--primary-color);
    font-weight: 700;
}
.meal-type-badge {
    font-size: 0.8rem;
    padding: 5px 12px;
    border-radius: 15px;
    color: white;
    font-weight: 600;
    text-transform: capitalize;
    margin-right: auto; /* Push to the left (for RTL) */
    margin-left: 10px; /* Space from food name */
}
.meal-type-badge.breakfast { background-color: #FFC107; /* Amber */ }
.meal-type-badge.lunch { background-color: #4CAF50; /* Green */ }
.meal-type-badge.dinner { background-color: #E53935; /* Red darker */ }
.meal-type-badge.snack { background-color: #5E35B1; /* Deep Purple */ }
[data-theme="dark"] .meal-type-badge.breakfast { background-color: #FFA000; }
[data-theme="dark"] .meal-type-badge.lunch { background-color: #388E3C; }
[data-theme="dark"] .meal-type-badge.dinner { background-color: #C62828; }
[data-theme="dark"] .meal-type-badge.snack { background-color: #4527A0; }

.meal-item-details { margin-bottom: 12px; }
.meal-item-details p { margin: 6px 0; font-size: 0.95rem; color: var(--text-color); line-height: 1.5; }
[data-theme="dark"] .meal-item-details p { color: #ccc; }

.meal-item-macros {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed var(--border-color);
}
.meal-item-macros > div {
    font-size: 0.9rem;
    color: var(--text-color);
    background-color: var(--background-color);
    padding: 6px 10px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
}
[data-theme="dark"] .meal-item-macros > div { color: #ddd; }
.meal-item-macros > div strong { color: var(--primary-color); margin-left: 4px; }
[data-theme="dark"] .meal-item-macros > div strong { color: var(--secondary-color); }

.meal-item-actions {
    display: flex;
    justify-content: flex-start; /* RTL: buttons to the right */
    gap: 10px;
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid var(--border-color);
}
.meal-item-actions button {
    background-color: transparent;
    border: 1px solid transparent;
    padding: 7px 14px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.2s ease;
}
.meal-item-actions button i { margin-left: 6px; } /* RTL: margin-right */
.meal-item-actions .edit-meal-btn { color: var(--primary-color); border-color: var(--primary-color); }
.meal-item-actions .edit-meal-btn:hover { background-color: var(--primary-color); color: white; }
.meal-item-actions .delete-meal-btn { color: var(--delete-color); border-color: var(--delete-color); }
.meal-item-actions .delete-meal-btn:hover { background-color: var(--delete-color); color: white; }

/* Responsive Adjustments */
@media (max-width: 992px) {
    .tracker-layout { grid-template-columns: 1fr; }
}
@media (max-width: 768px) {
    .calculator-card { padding: 20px; }
    .calculator-card h3 { font-size: 1.3rem; }
    .input-row { flex-direction: column; gap: 20px; }
    .input-row .input-group { min-width: 100%; }
    #daily-goals-content { grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; }
    #daily-goals-content .goal-item strong { font-size: 1.3rem; }
    .meal-item-header strong { font-size: 1.1rem; }
    .meal-type-badge { font-size: 0.75rem; padding: 4px 10px; }
}
@media (max-width: 480px) {
    .input-group label, .summary-item label { font-size: 0.9rem; }
    .input-group input, .input-group select { font-size: 0.95rem; padding: 10px 12px; }
    #add-meal-btn, #update-meal-btn, #cancel-edit-btn { font-size: 1rem; padding: 12px; }
    .progress-bar-container { height: 18px; }
    .progress-bar { line-height: 18px; font-size: 0.7rem; }
    .meal-item-header strong { font-size: 1.05rem; }
    .meal-item-macros > div { font-size: 0.85rem; padding: 5px 8px; }
}

/* Progress Chart Section */
.progress-chart-card {
    margin-top: 30px;
    border-top: 5px solid var(--secondary-color); /* تمييز مختلف */
}

.chart-container {
    padding: 10px;
    background-color: var(--background-color); /* خلفية طفيفة للرسم البياني */
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* Responsive for Chart Container if needed, though Chart.js handles responsiveness well */
@media (max-width: 768px) {
    .chart-container {
        height: 300px; /* تقليل الارتفاع قليلاً على الشاشات الأصغر */
    }
    .progress-chart-card h3 {
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .chart-container {
        height: 250px;
    }
}
