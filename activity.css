/* activity.css - Enhanced Styles for Physical Activity Tracker */

.activity-tracker-container { max-width: 1200px; margin: 0 auto; }
.activity-tracker-container h2 { text-align: center; color: var(--primary-color); margin-bottom: 15px; font-size: 1.8rem; }
.activity-tracker-container h2 i { margin-left: 10px; }
.activity-tracker-container .description { text-align: center; color: #888; margin-bottom: 30px; font-size: 1.1rem; }

.calculator-card { background-color: var(--card-background); padding: 25px 30px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.05); border: 1px solid var(--border-color); margin-bottom: 30px; }
.calculator-card h3 { text-align: center; color: var(--primary-color); margin-top: 0; margin-bottom: 25px; font-size: 1.5rem; }
.calculator-card h3 i { margin-left: 8px; }

/* Activity Goals Card */
.activity-goals-card { border-top: 5px solid var(--success-color); }
.activity-goals-card .input-row { display: flex; gap: 20px; margin-bottom: 15px; }
.activity-goals-card .input-row .input-group { flex: 1; }
#set-activity-goals-btn { width: auto; padding: 10px 25px; font-size: 1rem; display: block; margin: 15px auto 10px; background-color: var(--success-color); color: white; border:none; border-radius: 6px; cursor: pointer; }
#set-activity-goals-btn:hover { background-color: #388E3C; }
#set-activity-goals-btn i { margin-left: 8px; }
#current-weekly-goals-display p { margin: 5px 0; font-size: 1rem; color: var(--text-color); }
#current-weekly-goals-display strong { color: var(--primary-color); }


.tracker-layout { display: grid; grid-template-columns: 1fr 1.5fr; gap: 30px; align-items: flex-start; }

.activity-input-card .input-group { margin-bottom: 15px; }
.input-group label { display: block; margin-bottom: 8px; font-weight: 600; color: var(--text-color); }
.input-group input[type="text"], .input-group input[type="number"], .input-group input[type="date"], .input-group select, .input-group textarea { width: 100%; padding: 12px 15px; border: 1px solid var(--border-color); border-radius: 8px; font-size: 1rem; background-color: var(--background-color); color: var(--text-color); font-family: 'Cairo', sans-serif; }
.input-group textarea { resize: vertical; min-height: 60px; line-height: 1.5; }
.input-group input:focus, .input-group select:focus, .input-group textarea:focus { outline: none; border-color: var(--secondary-color); box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2); }
#activity-type { height: auto !important; } /* Override if select was single line */
.input-group input[type="checkbox"] { width: auto; margin-right: 8px; vertical-align: middle;}
.input-group label[for="manual-calories-toggle"] { display: inline-block !important; font-weight: normal; }


#log-activity-btn, #update-activity-btn, #cancel-edit-activity-btn { width: 100%; padding: 14px; border: none; border-radius: 8px; font-size: 1.1rem; cursor: pointer; transition: background-color 0.3s; font-weight: 600; display: flex; justify-content: center; align-items: center; background-color: var(--primary-color); color: #fff; margin-top: 10px; }
#log-activity-btn i, #update-activity-btn i, #cancel-edit-activity-btn i { margin-left: 8px; }
#log-activity-btn:hover { background-color: var(--secondary-color); }
#update-activity-btn { background-color: var(--success-color) !important; }
#update-activity-btn:hover { background-color: #388E3C !important; }
#cancel-edit-activity-btn { background-color: #757575 !important; }
#cancel-edit-activity-btn:hover { background-color: #616161 !important; }
.error-message { color: var(--delete-color); text-align: center; margin-top: 10px; font-weight: 600; min-height: 1em; }
.hidden { display: none !important; }
.subtle-text { font-size: 0.85rem; color: #888; margin-top: 5px; }
[data-theme="dark"] .subtle-text { color: #aaa; }

/* Summary & History Card */
.activity-summary-history-card #log-activity-date-picker { margin-bottom: 25px; }
.daily-activity-summary { background-color: var(--background-color); padding: 20px; border-radius: 8px; margin-bottom: 25px; border: 1px solid var(--border-color); }
.daily-activity-summary h4 { font-size: 1.2rem; color: var(--secondary-color); margin-bottom: 15px; text-align: right; }
.daily-activity-summary p { font-size: 1.1rem; margin-bottom: 8px; color: var(--text-color); }
.daily-activity-summary strong { font-weight: bold; color: var(--primary-color); margin-right: 5px; }

.activity-history-list-container h4 { font-size: 1.2rem; color: var(--secondary-color); margin-bottom: 15px; border-top: 1px dashed var(--border-color); padding-top: 20px; margin-top: 20px; }
#activity-history-list { list-style: none; padding: 0; max-height: 400px; overflow-y: auto; }
#activity-history-list li { background-color: var(--card-background); padding: 15px; border-radius: 8px; margin-bottom: 10px; border: 1px solid var(--border-color); box-shadow: 0 2px 4px rgba(0,0,0,0.04); }
#activity-history-list li.no-entries { text-align: center; color: #888; padding: 20px; background-color: transparent; border: none; box-shadow: none; }
.activity-item-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
.activity-item-header strong { font-size: 1.15rem; color: var(--primary-color); font-weight: 700; }
.activity-item-details { margin-bottom: 10px; font-size: 0.9rem; color: #555; }
[data-theme="dark"] .activity-item-details { color: #ccc; }
.activity-item-details span { font-weight: 600; color: var(--text-color); }
[data-theme="dark"] .activity-item-details span { color: var(--text-color); }
.activity-item-details .calories { color: var(--success-color); font-weight: bold; }
.activity-item-details .manual-entry-flag { font-size: 0.8em; color: #888; font-style: italic; margin-left: 5px;}
.activity-item-notes { font-size: 0.85rem; color: #777; margin-top: 8px; padding-top: 8px; border-top: 1px dashed var(--border-color); white-space: pre-wrap; }
[data-theme="dark"] .activity-item-notes { color: #bbb; border-top-color: var(--border-color); }
.activity-item-actions { display: flex; justify-content: flex-start; gap: 10px; margin-top: 10px; }
.activity-item-actions button { background: none; border: none; cursor: pointer; padding: 5px; font-size: 0.9rem; }
.activity-item-actions .edit-activity-btn { color: var(--secondary-color); }
.activity-item-actions .delete-activity-btn { color: var(--delete-color); }

/* Weekly Summary & Chart Card */
.weekly-activity-summary-card { margin-top: 30px; border-top: 5px solid var(--success-color); }
#weekly-activity-stats-content { padding: 10px 0; }
#weekly-activity-stats-content p { font-size: 1.05rem; margin-bottom: 12px; color: var(--text-color); display: flex; justify-content: space-between; align-items: center;}
#weekly-activity-stats-content strong { font-weight: bold; color: var(--primary-color); }
#weekly-activity-stats-content .progress-bar-container.small-progress { height: 15px; margin-top: 5px; margin-bottom: 10px;}
#weekly-activity-stats-content .progress-bar { border-radius: 6px; } /* Make progress bars more subtle */

.chart-container { padding: 10px; background-color: var(--background-color); border-radius: 8px; border: 1px solid var(--border-color); }

/* Responsive */
@media (max-width: 992px) { .tracker-layout { grid-template-columns: 1fr; } }
@media (max-width: 768px) {
    .calculator-card { padding: 20px; } .calculator-card h3 { font-size: 1.3rem; }
    .activity-goals-card .input-row { flex-direction: column; gap: 15px;}
    .daily-activity-summary p { font-size: 1rem; }
    .activity-item-header strong { font-size: 1.05rem; }
    #weekly-activity-stats-content p { font-size: 1rem; }
    .chart-container { height: 280px !important; }
}
@media (max-width: 480px) {
    .input-group label { font-size: 0.9rem; }
    .input-group input, .input-group select, .input-group textarea { font-size: 0.95rem; padding: 10px 12px; }
    #log-activity-btn, #update-activity-btn, #cancel-edit-activity-btn { font-size: 1rem; padding: 12px; }
    .chart-container { height: 220px !important; }
}